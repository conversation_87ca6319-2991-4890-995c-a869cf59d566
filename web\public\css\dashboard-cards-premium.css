/**
 * ========================================
 * DASHBOARD CARDS PREMIUM - UX COMERCIAL
 * Design otimizado para experiência comercial
 * ========================================
 */

/* ===== ANIMAÇÕES PREMIUM ===== */
@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes toggleBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-1px);
    }
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* ===== CARDS COM ANIMAÇÃO DE ENTRADA ===== */
.config-card {
    animation: cardSlideIn 0.4s ease-out;
    animation-fill-mode: both;
}

.config-card:nth-child(1) { animation-delay: 0.1s; }
.config-card:nth-child(2) { animation-delay: 0.15s; }
.config-card:nth-child(3) { animation-delay: 0.2s; }
.config-card:nth-child(4) { animation-delay: 0.25s; }
.config-card:nth-child(5) { animation-delay: 0.3s; }
.config-card:nth-child(6) { animation-delay: 0.35s; }

/* ===== EFEITOS VISUAIS PREMIUM ===== */
.config-card.active {
    border-color: rgba(0, 255, 127, 0.4);
    box-shadow: 
        0 8px 32px rgba(0, 255, 127, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.config-card.active::before {
    opacity: 1;
    background: linear-gradient(90deg, 
        var(--primary-green), 
        #00cc66, 
        var(--primary-green));
    animation: statusPulse 2s ease-in-out infinite;
}

/* ===== TOGGLE PREMIUM COM FEEDBACK VISUAL ===== */
.toggle input:checked + .toggle-slider {
    animation: toggleBounce 0.6s ease-out;
}

.toggle-group.primary .toggle input:checked + .toggle-slider {
    background: linear-gradient(135deg, var(--primary-green), #00ff7f);
    box-shadow: 
        0 0 12px rgba(0, 255, 127, 0.4),
        0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ===== MICRO-INTERAÇÕES ===== */
.config-title:hover .config-title-icon {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

.config-status.enabled {
    animation: statusPulse 3s ease-in-out infinite;
}

/* ===== ESTADOS DE LOADING ===== */
.config-card.loading {
    position: relative;
    pointer-events: none;
}

.config-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(2px);
}

.config-card.loading .config-content {
    opacity: 0.5;
}

/* ===== TOOLTIPS PREMIUM ===== */
.config-tooltip {
    position: relative;
    cursor: help;
}

.config-tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
    margin-bottom: 5px;
}

.config-tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
    margin-bottom: 1px;
}

.config-tooltip:hover::before,
.config-tooltip:hover::after {
    opacity: 1;
}

/* ===== INDICADORES DE DEPENDÊNCIA ===== */
.config-dependency {
    position: relative;
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 193, 7, 0.05);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 6px;
    font-size: 0.75rem;
    color: #ffc107;
}

.config-dependency::before {
    content: '⚠️';
    margin-right: 0.375rem;
}

.config-dependency.info {
    background: rgba(0, 123, 255, 0.05);
    border-color: rgba(0, 123, 255, 0.2);
    color: #007bff;
}

.config-dependency.info::before {
    content: 'ℹ️';
}

/* ===== PROGRESS INDICATORS ===== */
.config-progress {
    width: 100%;
    height: 3px;
    background: rgba(128, 128, 128, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 0.75rem;
}

.config-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-green), #00cc66);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.config-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== QUICK ACTIONS ===== */
.config-quick-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.config-card:hover .config-quick-actions {
    opacity: 1;
}

.quick-action-btn {
    padding: 0.25rem 0.5rem;
    background: rgba(0, 255, 127, 0.1);
    border: 1px solid rgba(0, 255, 127, 0.2);
    border-radius: 4px;
    color: var(--primary-green);
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background: rgba(0, 255, 127, 0.2);
    transform: translateY(-1px);
}

/* ===== DARK MODE OPTIMIZATIONS ===== */
@media (prefers-color-scheme: dark) {
    .config-card {
        background: linear-gradient(145deg, 
            rgba(18, 18, 18, 0.98), 
            rgba(12, 12, 12, 0.99));
    }
    
    .config-tooltip::before {
        background: rgba(0, 0, 0, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    .config-card {
        animation: none;
    }
    
    .toggle input:checked + .toggle-slider {
        animation: none;
    }
    
    .config-status.enabled {
        animation: none;
    }
    
    .config-progress-bar::after {
        animation: none;
    }
}

/* ===== FOCUS STATES FOR ACCESSIBILITY ===== */
.toggle input:focus + .toggle-slider {
    box-shadow: 0 0 0 3px rgba(0, 255, 127, 0.3);
    outline: none;
}

.config-card:focus-within {
    border-color: rgba(0, 255, 127, 0.4);
    box-shadow: 0 0 0 2px rgba(0, 255, 127, 0.2);
}
