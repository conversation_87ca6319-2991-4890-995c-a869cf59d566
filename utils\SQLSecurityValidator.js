/**
 * ========================================
 * CORREÇÃO CRÍTICA 6.2: VALIDADOR DE SEGURANÇA SQL
 * Utilitário para validar e sanitizar queries SQL
 * ========================================
 */

const validator = require('validator');

class SQLSecurityValidator {
    /**
     * Validar parâmetros de entrada para prevenir SQL injection
     */
    static validateInput(value, type = 'string', options = {}) {
        if (value === null || value === undefined) {
            return options.allowNull ? null : '';
        }

        switch (type) {
            case 'guildId':
            case 'userId':
            case 'channelId':
            case 'roleId':
            case 'messageId':
                return this.validateDiscordId(value);

            case 'string':
                return this.validateString(value, options);

            case 'number':
                return this.validateNumber(value, options);

            case 'boolean':
                return this.validateBoolean(value);

            case 'json':
                return this.validateJSON(value);

            case 'timestamp':
                return this.validateTimestamp(value);

            default:
                throw new Error(`Tipo de validação não suportado: ${type}`);
        }
    }

    /**
     * Validar IDs do Discord
     */
    static validateDiscordId(value) {
        if (!value) return null;
        
        const stringValue = String(value);
        
        // IDs do Discord devem ter entre 17-19 dígitos
        if (!validator.isLength(stringValue, { min: 17, max: 19 }) || 
            !validator.isNumeric(stringValue)) {
            throw new Error(`ID do Discord inválido: ${value}`);
        }
        
        return stringValue;
    }

    /**
     * Validar strings
     */
    static validateString(value, options = {}) {
        if (!value && !options.allowEmpty) return '';
        
        const stringValue = String(value);
        
        // Verificar comprimento
        if (options.maxLength && stringValue.length > options.maxLength) {
            throw new Error(`String muito longa: máximo ${options.maxLength} caracteres`);
        }
        
        if (options.minLength && stringValue.length < options.minLength) {
            throw new Error(`String muito curta: mínimo ${options.minLength} caracteres`);
        }
        
        // Sanitizar caracteres perigosos
        return this.sanitizeString(stringValue);
    }

    /**
     * Validar números
     */
    static validateNumber(value, options = {}) {
        const numValue = Number(value);
        
        if (isNaN(numValue)) {
            throw new Error(`Valor não é um número válido: ${value}`);
        }
        
        if (options.min !== undefined && numValue < options.min) {
            throw new Error(`Número muito pequeno: mínimo ${options.min}`);
        }
        
        if (options.max !== undefined && numValue > options.max) {
            throw new Error(`Número muito grande: máximo ${options.max}`);
        }
        
        return numValue;
    }

    /**
     * Validar booleanos
     */
    static validateBoolean(value) {
        if (typeof value === 'boolean') return value;
        if (value === 'true' || value === '1' || value === 1) return true;
        if (value === 'false' || value === '0' || value === 0) return false;
        
        throw new Error(`Valor booleano inválido: ${value}`);
    }

    /**
     * Validar JSON
     */
    static validateJSON(value) {
        if (!value) return '{}';
        
        if (typeof value === 'object') {
            return JSON.stringify(value);
        }
        
        try {
            JSON.parse(value);
            return value;
        } catch (error) {
            throw new Error(`JSON inválido: ${error.message}`);
        }
    }

    /**
     * Validar timestamp
     */
    static validateTimestamp(value) {
        if (!value) return new Date().toISOString();
        
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            throw new Error(`Timestamp inválido: ${value}`);
        }
        
        return date.toISOString();
    }

    /**
     * Sanitizar strings removendo caracteres perigosos
     */
    static sanitizeString(str) {
        if (!str || typeof str !== 'string') return '';
        
        // Remover caracteres de controle SQL
        return str
            .replace(/[\x00-\x1F\x7F]/g, '') // Caracteres de controle
            .replace(/['"`;\\]/g, '') // Caracteres SQL perigosos
            .trim();
    }

    /**
     * Validar query SQL para garantir que usa prepared statements
     */
    static validateQuery(query, params = []) {
        if (!query || typeof query !== 'string') {
            throw new Error('Query SQL inválida');
        }

        // Verificar se a query contém placeholders (?)
        const placeholderCount = (query.match(/\?/g) || []).length;
        
        if (placeholderCount !== params.length) {
            throw new Error(`Número de placeholders (${placeholderCount}) não corresponde ao número de parâmetros (${params.length})`);
        }

        // Verificar se não há concatenação de strings suspeita
        const suspiciousPatterns = [
            /\+\s*['"`]/,  // Concatenação com strings
            /\$\{.*\}/,    // Template literals
            /eval\s*\(/,   // Eval
            /exec\s*\(/,   // Exec
        ];

        for (const pattern of suspiciousPatterns) {
            if (pattern.test(query)) {
                throw new Error('Query contém padrões suspeitos de injeção SQL');
            }
        }

        return true;
    }

    /**
     * Wrapper seguro para execução de queries
     */
    static executeSecureQuery(db, query, params = [], types = []) {
        try {
            // Validar query
            this.validateQuery(query, params);

            // Validar parâmetros
            const validatedParams = params.map((param, index) => {
                const type = types[index] || 'string';
                return this.validateInput(param, type);
            });

            // Executar query com prepared statement
            const stmt = db.prepare(query);
            return stmt.all(...validatedParams);

        } catch (error) {
            console.error('❌ [SQL SECURITY] Erro na execução segura:', error);
            throw new Error(`Erro de segurança SQL: ${error.message}`);
        }
    }

    /**
     * Wrapper seguro para modificações (INSERT, UPDATE, DELETE)
     */
    static executeSecureModification(db, query, params = [], types = []) {
        try {
            // Validar query
            this.validateQuery(query, params);

            // Validar parâmetros
            const validatedParams = params.map((param, index) => {
                const type = types[index] || 'string';
                return this.validateInput(param, type);
            });

            // Executar query com prepared statement
            const stmt = db.prepare(query);
            const result = stmt.run(...validatedParams);

            return {
                lastID: result.lastInsertRowid,
                changes: result.changes
            };

        } catch (error) {
            console.error('❌ [SQL SECURITY] Erro na modificação segura:', error);
            throw new Error(`Erro de segurança SQL: ${error.message}`);
        }
    }

    /**
     * Verificar se uma string contém tentativas de injeção SQL
     */
    static detectSQLInjection(input) {
        if (!input || typeof input !== 'string') return false;

        const injectionPatterns = [
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
            /(--|\/\*|\*\/)/,
            /(\b(OR|AND)\b.*=.*)/i,
            /['"]\s*(OR|AND)\s*['"]/i,
            /;\s*(DROP|DELETE|INSERT|UPDATE)/i
        ];

        return injectionPatterns.some(pattern => pattern.test(input));
    }

    /**
     * Log de tentativa de injeção SQL
     */
    static logInjectionAttempt(input, source, userId = null, guildId = null) {
        const logData = {
            timestamp: new Date().toISOString(),
            input: input.substring(0, 100), // Primeiros 100 caracteres
            source: source,
            user_id: userId,
            guild_id: guildId,
            severity: 'CRITICAL'
        };

        console.error('🚨 [SQL INJECTION ATTEMPT] Tentativa de injeção SQL detectada:', logData);
        
        // Aqui você pode adicionar logging adicional, como enviar para um serviço de monitoramento
        return logData;
    }
}

module.exports = SQLSecurityValidator;
