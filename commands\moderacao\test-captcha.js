/**
 * ========================================
 * COMANDO DE TESTE PARA CAPTCHA DE IMAGEM
 * Comando para testar geração de captcha
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, AttachmentBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('test-captcha')
        .setDescription('🧪 Testar geração de captcha de imagem')
        .addStringOption(option =>
            option.setName('codigo')
                .setDescription('Código personalizado para o captcha (opcional)')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Gerar código do captcha
            const customCode = interaction.options.getString('codigo');
            const code = customCode || generateImageCaptchaCode();

            console.log(`🧪 [TEST CAPTCHA] Gerando captcha com código: ${code}`);

            // Gerar imagem do captcha
            const captchaBuffer = await generateCaptchaImage(code);
            const attachment = new AttachmentBuilder(captchaBuffer, { name: 'test-captcha.png' });

            const embed = new EmbedBuilder()
                .setTitle('🧪 Teste de Captcha de Imagem')
                .setDescription(`**Código gerado:** \`${code}\`\n\nEsta é uma imagem de teste do sistema de captcha.`)
                .setImage('attachment://test-captcha.png')
                .addFields(
                    { name: '📝 Instruções', value: 'Digite exatamente o código que você vê na imagem', inline: false },
                    { name: '🔧 Função', value: 'Este teste verifica se a geração de captcha está funcionando', inline: false }
                )
                .setColor('#ffa500')
                .setFooter({ text: 'Nodex | Moderação - Teste de Captcha' })
                .setTimestamp();

            await interaction.editReply({
                embeds: [embed],
                files: [attachment]
            });

            console.log(`✅ [TEST CAPTCHA] Captcha de teste enviado com sucesso`);

        } catch (error) {
            console.error('❌ [TEST CAPTCHA] Erro:', error);

            await interaction.editReply({
                content: `❌ Erro ao gerar captcha de teste:\n\`\`\`${error.message}\`\`\``
            });
        }
    }
};

/**
 * Gerar código aleatório para captcha de imagem
 */
function generateImageCaptchaCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * Gerar imagem de captcha
 */
async function generateCaptchaImage(code) {
    const { createCanvas } = require('canvas');

    // Criar canvas
    const width = 200;
    const height = 80;
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');

    // Fundo com gradiente
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#f0f0f0');
    gradient.addColorStop(1, '#e0e0e0');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Adicionar ruído de fundo
    for (let i = 0; i < 100; i++) {
        ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.1)`;
        ctx.fillRect(Math.random() * width, Math.random() * height, 2, 2);
    }

    // Configurar texto
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Desenhar cada caractere com rotação e cor aleatória
    const charWidth = width / code.length;
    for (let i = 0; i < code.length; i++) {
        const char = code[i];
        const x = charWidth * i + charWidth / 2;
        const y = height / 2;

        ctx.save();
        ctx.translate(x, y);
        ctx.rotate((Math.random() - 0.5) * 0.5); // Rotação aleatória

        // Cor aleatória para cada caractere
        const colors = ['#333333', '#666666', '#444444', '#555555'];
        ctx.fillStyle = colors[Math.floor(Math.random() * colors.length)];

        ctx.fillText(char, 0, 0);
        ctx.restore();
    }

    // Adicionar linhas de interferência
    for (let i = 0; i < 5; i++) {
        ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`;
        ctx.lineWidth = Math.random() * 3 + 1;
        ctx.beginPath();
        ctx.moveTo(Math.random() * width, Math.random() * height);
        ctx.lineTo(Math.random() * width, Math.random() * height);
        ctx.stroke();
    }

    return canvas.toBuffer('image/png');
}
