/**
 * ========================================
 * CORREÇÃO CRÍTICA 6.1: MIDDLEWARE DE PERMISSÕES
 * Sistema centralizado de validação de permissões
 * ========================================
 */

const { PermissionFlagsBits } = require('discord.js');
const HierarchyValidator = require('../../utils/HierarchyValidator');

class PermissionsMiddleware {
    /**
     * Verificar se usuário tem acesso ao servidor
     */
    static requireGuildAccess(req, res, next) {
        try {
            const { guildId } = req.params;
            const user = req.user;

            if (!user) {
                return res.status(401).json({ 
                    error: 'Usuário não autenticado',
                    code: 'UNAUTHORIZED'
                });
            }

            if (!guildId) {
                return res.status(400).json({ 
                    error: 'Guild ID não fornecido',
                    code: 'MISSING_GUILD_ID'
                });
            }

            // Verificar se o usuário tem acesso ao servidor
            const guild = req.app.locals.client?.guilds?.cache?.get(guildId);
            if (!guild) {
                return res.status(404).json({ 
                    error: 'Servidor não encontrado',
                    code: 'GUILD_NOT_FOUND'
                });
            }

            const member = guild.members.cache.get(user.id);
            if (!member) {
                return res.status(403).json({ 
                    error: 'Você não é membro deste servidor',
                    code: 'NOT_GUILD_MEMBER'
                });
            }

            // Adicionar informações ao request
            req.guild = guild;
            req.member = member;

            next();
        } catch (error) {
            console.error('❌ [PERMISSIONS] Erro na verificação de acesso ao servidor:', error);
            res.status(500).json({ 
                error: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Verificar permissões específicas
     */
    static requirePermissions(permissions = []) {
        return (req, res, next) => {
            try {
                const { member } = req;

                if (!member) {
                    return res.status(403).json({ 
                        error: 'Acesso ao servidor necessário',
                        code: 'GUILD_ACCESS_REQUIRED'
                    });
                }

                // Verificar se é administrador (bypass)
                if (member.permissions.has(PermissionFlagsBits.Administrator)) {
                    req.isAdmin = true;
                    return next();
                }

                // Verificar permissões específicas
                const hasPermissions = permissions.every(permission => 
                    member.permissions.has(permission)
                );

                if (!hasPermissions) {
                    // Log tentativa de acesso não autorizado
                    console.warn('🚨 [PERMISSIONS] Acesso negado:', {
                        userId: member.user.id,
                        username: member.user.tag,
                        guildId: member.guild.id,
                        requiredPermissions: permissions.map(p => Object.keys(PermissionFlagsBits).find(key => PermissionFlagsBits[key] === p)),
                        userPermissions: member.permissions.toArray()
                    });

                    return res.status(403).json({ 
                        error: 'Permissões insuficientes',
                        code: 'INSUFFICIENT_PERMISSIONS',
                        required: permissions.map(p => Object.keys(PermissionFlagsBits).find(key => PermissionFlagsBits[key] === p))
                    });
                }

                next();
            } catch (error) {
                console.error('❌ [PERMISSIONS] Erro na verificação de permissões:', error);
                res.status(500).json({ 
                    error: 'Erro interno do servidor',
                    code: 'INTERNAL_ERROR'
                });
            }
        };
    }

    /**
     * Verificar se usuário pode moderar outro usuário
     */
    static requireModerationPermissions(req, res, next) {
        try {
            const { member, guild } = req;
            const { targetUserId } = req.body || req.params;

            if (!member || !guild) {
                return res.status(403).json({ 
                    error: 'Acesso ao servidor necessário',
                    code: 'GUILD_ACCESS_REQUIRED'
                });
            }

            // Se não há usuário alvo, apenas verificar permissões básicas
            if (!targetUserId) {
                const hasModPermissions = member.permissions.has(PermissionFlagsBits.ModerateMembers) ||
                                        member.permissions.has(PermissionFlagsBits.BanMembers) ||
                                        member.permissions.has(PermissionFlagsBits.KickMembers) ||
                                        member.permissions.has(PermissionFlagsBits.Administrator);

                if (!hasModPermissions) {
                    return res.status(403).json({ 
                        error: 'Permissões de moderação necessárias',
                        code: 'MODERATION_PERMISSIONS_REQUIRED'
                    });
                }

                return next();
            }

            // Verificar hierarquia se há usuário alvo
            guild.members.fetch(targetUserId).then(async (targetMember) => {
                const hierarchyValidation = await HierarchyValidator.validateHierarchy(
                    member, 
                    targetMember, 
                    guild, 
                    'moderate'
                );

                if (!hierarchyValidation.canModerate) {
                    // Log tentativa de escalação de privilégios
                    HierarchyValidator.logPrivilegeEscalationAttempt(
                        member, 
                        targetMember, 
                        'web_moderation', 
                        hierarchyValidation.reason, 
                        guild
                    );

                    return res.status(403).json({ 
                        error: hierarchyValidation.message,
                        code: 'HIERARCHY_VIOLATION',
                        reason: hierarchyValidation.reason
                    });
                }

                req.targetMember = targetMember;
                next();
            }).catch(error => {
                console.error('❌ [PERMISSIONS] Erro ao buscar membro alvo:', error);
                res.status(404).json({ 
                    error: 'Usuário não encontrado no servidor',
                    code: 'TARGET_USER_NOT_FOUND'
                });
            });

        } catch (error) {
            console.error('❌ [PERMISSIONS] Erro na verificação de moderação:', error);
            res.status(500).json({ 
                error: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Verificar se usuário pode acessar configurações específicas
     */
    static requireConfigAccess(configSection = null) {
        return (req, res, next) => {
            try {
                const { member } = req;

                if (!member) {
                    return res.status(403).json({ 
                        error: 'Acesso ao servidor necessário',
                        code: 'GUILD_ACCESS_REQUIRED'
                    });
                }

                // Administradores podem acessar tudo
                if (member.permissions.has(PermissionFlagsBits.Administrator)) {
                    return next();
                }

                // Verificar permissões específicas por seção
                const sectionPermissions = {
                    'moderacao': [PermissionFlagsBits.ModerateMembers, PermissionFlagsBits.ManageMessages],
                    'logs': [PermissionFlagsBits.ViewAuditLog, PermissionFlagsBits.ManageGuild],
                    'verificacao': [PermissionFlagsBits.ManageRoles, PermissionFlagsBits.ManageChannels],
                    'ia': [PermissionFlagsBits.ModerateMembers, PermissionFlagsBits.ManageMessages],
                    'anti_raid': [PermissionFlagsBits.BanMembers, PermissionFlagsBits.ManageGuild],
                    'analytics': [PermissionFlagsBits.ViewAuditLog],
                    'automod': [PermissionFlagsBits.ModerateMembers, PermissionFlagsBits.ManageMessages]
                };

                if (configSection && sectionPermissions[configSection]) {
                    const hasRequiredPermissions = sectionPermissions[configSection].some(permission => 
                        member.permissions.has(permission)
                    );

                    if (!hasRequiredPermissions) {
                        return res.status(403).json({ 
                            error: `Permissões insuficientes para acessar configurações de ${configSection}`,
                            code: 'INSUFFICIENT_CONFIG_PERMISSIONS',
                            section: configSection
                        });
                    }
                }

                // Verificar permissão geral de gerenciar servidor
                if (!member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                    return res.status(403).json({ 
                        error: 'Permissão de gerenciar servidor necessária',
                        code: 'MANAGE_GUILD_REQUIRED'
                    });
                }

                next();
            } catch (error) {
                console.error('❌ [PERMISSIONS] Erro na verificação de acesso a configurações:', error);
                res.status(500).json({ 
                    error: 'Erro interno do servidor',
                    code: 'INTERNAL_ERROR'
                });
            }
        };
    }

    /**
     * Verificar se usuário é proprietário do servidor
     */
    static requireOwner(req, res, next) {
        try {
            const { member, guild } = req;

            if (!member || !guild) {
                return res.status(403).json({ 
                    error: 'Acesso ao servidor necessário',
                    code: 'GUILD_ACCESS_REQUIRED'
                });
            }

            if (member.id !== guild.ownerId) {
                return res.status(403).json({ 
                    error: 'Apenas o proprietário do servidor pode realizar esta ação',
                    code: 'OWNER_ONLY'
                });
            }

            next();
        } catch (error) {
            console.error('❌ [PERMISSIONS] Erro na verificação de proprietário:', error);
            res.status(500).json({ 
                error: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR'
            });
        }
    }

    /**
     * Log de ações administrativas
     */
    static logAdminAction(action) {
        return (req, res, next) => {
            const originalSend = res.send;
            
            res.send = function(data) {
                // Log apenas se a ação foi bem-sucedida
                if (res.statusCode < 400) {
                    console.log('📋 [ADMIN ACTION]', {
                        action,
                        userId: req.member?.user?.id,
                        username: req.member?.user?.tag,
                        guildId: req.guild?.id,
                        guildName: req.guild?.name,
                        timestamp: new Date().toISOString(),
                        ip: req.ip,
                        userAgent: req.get('User-Agent')
                    });
                }
                
                originalSend.call(this, data);
            };
            
            next();
        };
    }
}

module.exports = PermissionsMiddleware;
