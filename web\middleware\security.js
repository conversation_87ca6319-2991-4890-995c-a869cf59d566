/**
 * ========================================
 * MIDDLEWARE DE SEGURANÇA AVANÇADO
 * Sistema completo de proteção para o dashboard
 * ========================================
 */

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const ExpressBrute = require('express-brute');
const MemoryStore = require('express-brute/lib/MemoryStore');
const csrf = require('csurf');
const validator = require('validator');
const xss = require('xss');
const sanitizeHtml = require('sanitize-html');
const { body, validationResult, param, query } = require('express-validator');
const winston = require('winston');
const path = require('path');

class SecurityMiddleware {
    constructor() {
        this.setupLogger();
        this.setupBruteForce();
        this.setupRateLimiting();
        this.setupCSRF();
    }

    /**
     * Configurar logger de segurança
     */
    setupLogger() {
        this.securityLogger = winston.createLogger({
            level: 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json()
            ),
            transports: [
                new winston.transports.File({ 
                    filename: path.join(__dirname, '../../logs/security.log'),
                    maxsize: 5242880, // 5MB
                    maxFiles: 5
                }),
                new winston.transports.Console({
                    format: winston.format.simple()
                })
            ]
        });
    }

    /**
     * Configurar proteção contra força bruta
     */
    setupBruteForce() {
        const store = new MemoryStore();
        
        this.bruteForce = new ExpressBrute(store, {
            freeRetries: 5,
            minWait: 5 * 60 * 1000, // 5 minutos
            maxWait: 60 * 60 * 1000, // 1 hora
            lifetime: 24 * 60 * 60, // 24 horas
            failCallback: (req, res, next, nextValidRequestDate) => {
                this.logSecurityEvent('BRUTE_FORCE_BLOCKED', {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                    path: req.path,
                    nextValidRequestDate
                });
                
                res.status(429).json({
                    error: 'Muitas tentativas de login. Tente novamente mais tarde.',
                    nextValidRequestDate: nextValidRequestDate
                });
            }
        });

        // Proteção específica para login
        this.loginBruteForce = new ExpressBrute(store, {
            freeRetries: 3,
            minWait: 10 * 60 * 1000, // 10 minutos
            maxWait: 2 * 60 * 60 * 1000, // 2 horas
            lifetime: 24 * 60 * 60,
            failCallback: (req, res, next, nextValidRequestDate) => {
                this.logSecurityEvent('LOGIN_BRUTE_FORCE_BLOCKED', {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                    nextValidRequestDate
                });
                
                res.redirect('/login?error=too_many_attempts');
            }
        });
    }

    /**
     * Configurar rate limiting
     */
    setupRateLimiting() {
        // Rate limit geral
        this.generalRateLimit = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutos
            max: 100, // máximo 100 requests por IP
            message: {
                error: 'Muitas requisições. Tente novamente em 15 minutos.'
            },
            standardHeaders: true,
            legacyHeaders: false,
            handler: (req, res) => {
                this.logSecurityEvent('RATE_LIMIT_EXCEEDED', {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                    path: req.path
                });
                
                res.status(429).json({
                    error: 'Muitas requisições. Tente novamente em 15 minutos.'
                });
            }
        });

        // Rate limit para API
        this.apiRateLimit = rateLimit({
            windowMs: 5 * 60 * 1000, // 5 minutos
            max: 50, // máximo 50 requests por IP
            message: {
                error: 'Muitas requisições à API. Tente novamente em 5 minutos.'
            },
            standardHeaders: true,
            legacyHeaders: false
        });

        // Rate limit para login
        this.loginRateLimit = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutos
            max: 5, // máximo 5 tentativas de login por IP
            message: {
                error: 'Muitas tentativas de login. Tente novamente em 15 minutos.'
            },
            skipSuccessfulRequests: true
        });
    }

    /**
     * Configurar CSRF
     */
    setupCSRF() {
        this.csrfProtection = csrf({
            cookie: {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict'
            },
            ignoreMethods: ['GET', 'HEAD', 'OPTIONS']
        });
    }

    /**
     * Configurar Helmet com CSP
     */
    getHelmetConfig() {
        return helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: [
                        "'self'", 
                        "'unsafe-inline'", 
                        "https://cdnjs.cloudflare.com",
                        "https://fonts.googleapis.com"
                    ],
                    scriptSrc: [
                        "'self'", 
                        "'unsafe-inline'",
                        "https://cdnjs.cloudflare.com",
                        "https://cdn.jsdelivr.net"
                    ],
                    fontSrc: [
                        "'self'", 
                        "https://fonts.gstatic.com",
                        "https://cdnjs.cloudflare.com"
                    ],
                    imgSrc: [
                        "'self'", 
                        "data:", 
                        "https://cdn.discordapp.com",
                        "https://images.unsplash.com"
                    ],
                    connectSrc: ["'self'"],
                    frameSrc: ["'none'"],
                    objectSrc: ["'none'"],
                    upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
                }
            },
            hsts: {
                maxAge: 31536000,
                includeSubDomains: true,
                preload: true
            },
            noSniff: true,
            frameguard: { action: 'deny' },
            xssFilter: true,
            referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
            // CORREÇÃO CRÍTICA 7.2: Headers adicionais de segurança
            crossOriginEmbedderPolicy: false,
            crossOriginOpenerPolicy: { policy: 'same-origin' },
            crossOriginResourcePolicy: { policy: 'cross-origin' },
            dnsPrefetchControl: { allow: false },
            expectCt: {
                maxAge: 86400,
                enforce: process.env.NODE_ENV === 'production'
            },
            permittedCrossDomainPolicies: false
        });
    }

    /**
     * Middleware de sanitização de entrada
     */
    sanitizeInput = (req, res, next) => {
        // Sanitizar body
        if (req.body && typeof req.body === 'object') {
            req.body = this.deepSanitize(req.body);
        }

        // Sanitizar query parameters
        if (req.query && typeof req.query === 'object') {
            req.query = this.deepSanitize(req.query);
        }

        // Sanitizar params
        if (req.params && typeof req.params === 'object') {
            req.params = this.deepSanitize(req.params);
        }

        next();
    }

    /**
     * Sanitização profunda de objetos
     */
    deepSanitize(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return typeof obj === 'string' ? this.sanitizeString(obj) : obj;
        }

        const sanitized = {};
        for (const [key, value] of Object.entries(obj)) {
            const sanitizedKey = this.sanitizeString(key);
            
            if (Array.isArray(value)) {
                sanitized[sanitizedKey] = value.map(item => this.deepSanitize(item));
            } else if (typeof value === 'object' && value !== null) {
                sanitized[sanitizedKey] = this.deepSanitize(value);
            } else {
                sanitized[sanitizedKey] = typeof value === 'string' ? this.sanitizeString(value) : value;
            }
        }
        
        return sanitized;
    }

    /**
     * Sanitizar string individual
     */
    sanitizeString(str) {
        if (typeof str !== 'string') return str;
        
        // Remover XSS
        let sanitized = xss(str, {
            whiteList: {}, // Não permitir nenhuma tag HTML
            stripIgnoreTag: true,
            stripIgnoreTagBody: ['script']
        });

        // Sanitizar HTML adicional
        sanitized = sanitizeHtml(sanitized, {
            allowedTags: [],
            allowedAttributes: {}
        });

        // Escapar caracteres especiais SQL
        sanitized = sanitized.replace(/['";\\]/g, '\\$&');

        return sanitized;
    }

    /**
     * Log de eventos de segurança
     */
    logSecurityEvent(event, details) {
        this.securityLogger.warn('Security Event', {
            event,
            timestamp: new Date().toISOString(),
            ...details
        });
    }

    /**
     * Middleware de validação de Guild ID
     */
    validateGuildId = [
        param('guildId').isLength({ min: 17, max: 19 }).isNumeric().withMessage('Guild ID inválido'),
        (req, res, next) => {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                this.logSecurityEvent('INVALID_GUILD_ID', {
                    ip: req.ip,
                    guildId: req.params.guildId,
                    errors: errors.array()
                });
                return res.status(400).json({ error: 'Guild ID inválido' });
            }
            next();
        }
    ];

    /**
     * Middleware de validação de configuração
     */
    validateConfig = [
        body('*').custom((value, { path }) => {
            if (typeof value === 'string' && value.length > 1000) {
                throw new Error(`Campo ${path} muito longo`);
            }
            return true;
        }),
        (req, res, next) => {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                this.logSecurityEvent('INVALID_CONFIG_DATA', {
                    ip: req.ip,
                    guildId: req.params.guildId,
                    errors: errors.array()
                });
                return res.status(400).json({ 
                    error: 'Dados de configuração inválidos',
                    details: errors.array()
                });
            }
            next();
        }
    ];

    /**
     * CORREÇÃO CRÍTICA 7.5: Middleware de auditoria aprimorado
     */
    auditLog = (action) => {
        return (req, res, next) => {
            const startTime = Date.now();
            const originalSend = res.send;

            res.send = function(data) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                // Log detalhado da ação
                const auditData = {
                    action,
                    userId: req.user?.id,
                    username: req.user?.username,
                    guildId: req.params?.guildId,
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                    statusCode: res.statusCode,
                    method: req.method,
                    url: req.originalUrl,
                    duration: duration,
                    timestamp: new Date().toISOString(),
                    sessionId: req.sessionID,
                    referer: req.get('Referer'),
                    // Dados sanitizados do request
                    requestData: req.securityMiddleware?.sanitizeAuditData({
                        body: req.body,
                        query: req.query,
                        params: req.params
                    })
                };

                // Log baseado no status da resposta
                if (res.statusCode < 400) {
                    req.securityMiddleware.logSecurityEvent('AUDIT_SUCCESS', auditData);
                } else if (res.statusCode >= 400 && res.statusCode < 500) {
                    req.securityMiddleware.logSecurityEvent('AUDIT_CLIENT_ERROR', auditData);
                } else {
                    req.securityMiddleware.logSecurityEvent('AUDIT_SERVER_ERROR', auditData);
                }

                originalSend.call(this, data);
            };

            req.securityMiddleware = this;
            next();
        };
    }

    /**
     * CORREÇÃO CRÍTICA 7.5: Sanitizar dados para auditoria
     */
    sanitizeAuditData(data) {
        if (!data || typeof data !== 'object') return data;

        const sensitiveFields = [
            'password', 'token', 'secret', 'key', 'auth', 'authorization',
            'cookie', 'session', 'csrf', 'api_key', 'access_token', 'refresh_token'
        ];

        const sanitized = JSON.parse(JSON.stringify(data));

        const sanitizeObject = (obj) => {
            if (!obj || typeof obj !== 'object') return obj;

            for (const [key, value] of Object.entries(obj)) {
                const keyLower = key.toLowerCase();
                const isSensitive = sensitiveFields.some(field => keyLower.includes(field));

                if (isSensitive) {
                    obj[key] = '[REDACTED]';
                } else if (typeof value === 'object' && value !== null) {
                    sanitizeObject(value);
                } else if (typeof value === 'string' && value.length > 500) {
                    obj[key] = value.substring(0, 500) + '...[TRUNCATED]';
                }
            }
        };

        sanitizeObject(sanitized);
        return sanitized;
    }
}

module.exports = SecurityMiddleware;
