/**
 * ========================================
 * CORREÇÃO CRÍTICA 3.1: VALIDADOR DE HIERARQUIA
 * Utilitário para verificar hierarquia de cargos e prevenir escalação de privilégios
 * ========================================
 */

const { PermissionFlagsBits } = require('discord.js');

class HierarchyValidator {
    /**
     * Verificar se um moderador pode punir um usuário alvo
     * @param {GuildMember} moderator - Membro que está executando a ação
     * @param {GuildMember|User} target - Alvo da ação
     * @param {Guild} guild - Servidor
     * @param {string} action - Tipo de ação (ban, kick, timeout, etc.)
     * @returns {Object} Resultado da validação
     */
    static async validateHierarchy(moderator, target, guild, action = 'moderate') {
        try {
            // Verificar se o alvo é um membro do servidor
            let targetMember = null;
            if (target.id) {
                try {
                    targetMember = await guild.members.fetch(target.id);
                } catch (error) {
                    // Usuário não está no servidor
                }
            } else if (target.roles) {
                targetMember = target; // Já é um GuildMember
            }

            // 1. Verificar auto-moderação
            if (moderator.id === target.id) {
                return {
                    canModerate: false,
                    reason: 'self_moderation',
                    message: 'Você não pode aplicar ações de moderação em si mesmo.'
                };
            }

            // 2. Verificar se está tentando moderar o bot
            if (target.id === guild.client?.user?.id) {
                return {
                    canModerate: false,
                    reason: 'bot_moderation',
                    message: 'Não é possível aplicar ações de moderação no bot.'
                };
            }

            // 3. Verificar se o alvo é o dono do servidor
            if (target.id === guild.ownerId) {
                return {
                    canModerate: false,
                    reason: 'owner_protection',
                    message: 'Não é possível moderar o proprietário do servidor.'
                };
            }

            // Se o usuário não está no servidor, permitir (para casos como ban de usuários externos)
            if (!targetMember) {
                return {
                    canModerate: true,
                    reason: 'external_user',
                    message: 'Usuário não está no servidor - ação permitida.'
                };
            }

            // 4. Verificar se o alvo é administrador
            if (targetMember.permissions.has(PermissionFlagsBits.Administrator)) {
                // Apenas o dono pode moderar administradores
                if (moderator.id !== guild.ownerId) {
                    return {
                        canModerate: false,
                        reason: 'admin_protection',
                        message: 'Apenas o proprietário do servidor pode moderar administradores.'
                    };
                }
            }

            // 5. Verificar hierarquia de cargos
            const moderatorHighestRole = moderator.roles.highest;
            const targetHighestRole = targetMember.roles.highest;

            if (targetHighestRole.position >= moderatorHighestRole.position) {
                return {
                    canModerate: false,
                    reason: 'hierarchy_insufficient',
                    message: 'Você não pode moderar usuários com cargo igual ou superior ao seu.'
                };
            }

            // 6. Verificar se o bot pode moderar o alvo
            const botMember = guild.members.me;
            if (targetHighestRole.position >= botMember.roles.highest.position) {
                return {
                    canModerate: false,
                    reason: 'bot_hierarchy_insufficient',
                    message: 'O bot não pode moderar usuários com cargo igual ou superior ao dele.'
                };
            }

            // 7. Verificações específicas por ação
            const actionValidation = this.validateSpecificAction(moderator, targetMember, action);
            if (!actionValidation.canModerate) {
                return actionValidation;
            }

            // Todas as verificações passaram
            return {
                canModerate: true,
                reason: 'hierarchy_valid',
                message: 'Hierarquia válida - ação permitida.',
                targetMember: targetMember
            };

        } catch (error) {
            console.error('❌ [HIERARCHY VALIDATOR] Erro na validação:', error);
            return {
                canModerate: false,
                reason: 'validation_error',
                message: 'Erro interno na validação de hierarquia.'
            };
        }
    }

    /**
     * Validações específicas por tipo de ação
     */
    static validateSpecificAction(moderator, targetMember, action) {
        switch (action) {
            case 'ban':
                if (!targetMember.bannable) {
                    return {
                        canModerate: false,
                        reason: 'not_bannable',
                        message: 'Este usuário não pode ser banido (proteções do Discord).'
                    };
                }
                break;

            case 'kick':
                if (!targetMember.kickable) {
                    return {
                        canModerate: false,
                        reason: 'not_kickable',
                        message: 'Este usuário não pode ser expulso (proteções do Discord).'
                    };
                }
                break;

            case 'timeout':
                if (!targetMember.moderatable) {
                    return {
                        canModerate: false,
                        reason: 'not_moderatable',
                        message: 'Este usuário não pode receber timeout (proteções do Discord).'
                    };
                }
                break;

            case 'role_manage':
                // Verificar se o moderador pode gerenciar cargos
                if (!moderator.permissions.has(PermissionFlagsBits.ManageRoles)) {
                    return {
                        canModerate: false,
                        reason: 'insufficient_permissions',
                        message: 'Você não tem permissão para gerenciar cargos.'
                    };
                }
                break;
        }

        return { canModerate: true };
    }

    /**
     * Verificar se um usuário pode executar uma ação específica
     */
    static canExecuteAction(member, action, requiredPermissions = []) {
        // Verificar permissões específicas
        if (requiredPermissions.length > 0) {
            const hasPermissions = requiredPermissions.every(perm => 
                member.permissions.has(perm)
            );
            
            if (!hasPermissions) {
                return {
                    canExecute: false,
                    reason: 'insufficient_permissions',
                    message: `Permissões necessárias: ${requiredPermissions.join(', ')}`
                };
            }
        }

        // Verificar se é administrador (pode fazer tudo)
        if (member.permissions.has(PermissionFlagsBits.Administrator)) {
            return {
                canExecute: true,
                reason: 'administrator',
                message: 'Usuário é administrador.'
            };
        }

        return {
            canExecute: true,
            reason: 'permissions_valid',
            message: 'Permissões válidas.'
        };
    }

    /**
     * Log de tentativa de escalação de privilégios
     */
    static logPrivilegeEscalationAttempt(moderator, target, action, reason, guild) {
        const logData = {
            timestamp: new Date().toISOString(),
            guild_id: guild.id,
            guild_name: guild.name,
            moderator_id: moderator.id,
            moderator_tag: moderator.user.tag,
            target_id: target.id,
            target_tag: target.tag || target.user?.tag,
            action: action,
            reason: reason,
            severity: 'HIGH'
        };

        console.warn('🚨 [SECURITY] Tentativa de escalação de privilégios detectada:', logData);
        
        // Log no sistema de logs do bot
        if (guild.client?.logger) {
            guild.client.logger.warn('Tentativa de escalação de privilégios', logData);
        }

        return logData;
    }
}

module.exports = HierarchyValidator;
