/**
 * ========================================
 * EVENTO: MESSAGE DELETE
 * Monitora mensagens deletadas
 * ========================================
 */

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'messageDelete',
    async execute(message, client) {
        // Ignorar mensagens de bots
        if (message.author?.bot) return;

        // Ignorar mensagens em DM
        if (!message.guild) return;

        // Ignorar mensagens sem autor (muito antigas ou parciais)
        if (!message.author) return;

        // Ignorar mensagens sem conteúdo (embeds, anexos apenas)
        if (!message.content && (!message.attachments || message.attachments.size === 0)) return;

        try {
            // Obter configuração do servidor do cache E do banco
            let guildConfig = client.guildConfigs?.get(message.guild.id);

            // Se não há cache, buscar do banco
            if (!guildConfig) {
                guildConfig = await client.database.getGuildConfig(message.guild.id);
                if (guildConfig && client.guildConfigs) {
                    client.guildConfigs.set(message.guild.id, guildConfig);
                }
            }

            // Parse das configurações se necessário
            let settings = {};
            if (guildConfig?.settings) {
                try {
                    if (typeof guildConfig.settings === 'string') {
                        settings = JSON.parse(guildConfig.settings);
                    } else {
                        settings = guildConfig.settings;
                    }
                } catch (error) {
                    console.error('❌ [MESSAGE DELETE] Erro ao fazer parse das configurações:', error);
                    settings = {};
                }
            }

            // Verificar se logs estão habilitados (buscar em múltiplas fontes)
            const logsEnabled = settings.logs_enabled ||
                               settings.log_enabled ||
                               guildConfig?.logs_enabled ||
                               guildConfig?.log_enabled ||
                               true; // Padrão habilitado

            if (!logsEnabled) {
                console.log(`📝 [MESSAGE DELETE] Logs desabilitados para ${message.guild.name}`);
                return;
            }

            // Verificar se canal de logs está configurado (buscar em TODAS as fontes possíveis)
            const logChannelId = settings.general_log_channel ||
                                settings.message_log_channel ||
                                settings.log_channel ||
                                guildConfig?.automod_log_channel ||
                                guildConfig?.general_log_channel ||
                                guildConfig?.message_log_channel ||
                                guildConfig?.log_channel_id ||
                                guildConfig?.log_channel ||
                                guildConfig?.mod_log_channel_id;

            if (!logChannelId) {
                // Só mostrar aviso a cada 5 minutos para evitar spam
                const debugKey = `log_debug_${message.guild.id}`;
                const lastDebug = client.debugTimestamps?.get(debugKey) || 0;
                const now = Date.now();

                if (now - lastDebug > 5 * 60 * 1000) { // 5 minutos
                    console.log(`📝 [MESSAGE DELETE] Canal de logs não configurado para ${message.guild.name}`);
                    console.log(`📝 [MESSAGE DELETE] Configurações disponíveis:`, {
                        settings_general_log_channel: settings.general_log_channel,
                        guildConfig_log_channel_id: guildConfig?.log_channel_id,
                        cache_available: !!client.guildConfigs?.get(message.guild.id)
                    });
                    console.log(`📝 [MESSAGE DELETE] Configure no dashboard em "Logs & Auditoria" > "Canal de Logs Gerais"`);
                    if (!client.debugTimestamps) client.debugTimestamps = new Map();
                    client.debugTimestamps.set(debugKey, now);
                }
                return;
            }

            const logChannel = message.guild.channels.cache.get(logChannelId);

            if (!logChannel) {
                console.log(`📝 [MESSAGE DELETE] Canal de logs não encontrado: ${logChannelId}`);
                return;
            }

            console.log(`📝 [MESSAGE DELETE] Enviando log para #${logChannel.name} em ${message.guild.name}`);

            // Criar embed de log
            const deleteEmbed = new EmbedBuilder()
                .setColor('#ff4757')
                .setTitle(`🗑️ Mensagem Deletada - ${message.author.username}`)
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação - Sistema de Logs' });

            // Informações do usuário (agora garantimos que existe)
            deleteEmbed.addFields(
                { name: '👤 Usuário', value: `${message.author.tag} (${message.author.id})`, inline: true },
                { name: '📺 Canal', value: `${message.channel} (${message.channel.name})`, inline: true },
                { name: '📅 Data', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            );

            // Avatar do usuário
            deleteEmbed.setThumbnail(message.author.displayAvatarURL());

            // Conteúdo da mensagem
            if (message.content && message.content.trim().length > 0) {
                const content = message.content.length > 1000
                    ? message.content.substring(0, 997) + '...'
                    : message.content;

                deleteEmbed.addFields({
                    name: '📝 Conteúdo Original',
                    value: `\`\`\`${content}\`\`\``,
                    inline: false
                });
            } else if (message.attachments && message.attachments.size > 0) {
                deleteEmbed.addFields({
                    name: '📝 Conteúdo',
                    value: '*Mensagem continha apenas anexos*',
                    inline: false
                });
            } else {
                deleteEmbed.addFields({
                    name: '📝 Conteúdo',
                    value: '*Conteúdo vazio ou não disponível*',
                    inline: false
                });
            }

            // Anexos (se houver)
            if (message.attachments && message.attachments.size > 0) {
                const attachments = message.attachments.map(att => `[${att.name}](${att.url})`).join('\n');
                deleteEmbed.addFields({
                    name: '📎 Anexos',
                    value: attachments,
                    inline: false
                });
            }

            // Embeds (se houver)
            if (message.embeds && message.embeds.length > 0) {
                deleteEmbed.addFields({
                    name: '🔗 Embeds',
                    value: `${message.embeds.length} embed(s) anexado(s)`,
                    inline: true
                });
            }

            // ID da mensagem para referência
            deleteEmbed.addFields({
                name: '🆔 ID da Mensagem',
                value: `\`${message.id}\``,
                inline: true
            });

            await logChannel.send({ embeds: [deleteEmbed] });

            client.logger.info(`Mensagem deletada logada: ${message.author?.tag} em #${message.channel?.name}`);

        } catch (error) {
            client.logger.error('Erro ao logar mensagem deletada:', error);
        }
    }
};
