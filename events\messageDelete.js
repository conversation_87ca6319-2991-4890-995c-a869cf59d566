/**
 * ========================================
 * EVENTO: MESSAGE DELETE
 * Monitora mensagens deletadas
 * ========================================
 */

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'messageDelete',
    async execute(message, client) {
        // Ignorar mensagens de bots
        if (message.author?.bot) return;

        // Ignorar mensagens em DM
        if (!message.guild) return;

        // Ignorar mensagens sem autor (muito antigas ou parciais)
        if (!message.author) return;

        // Ignorar mensagens sem conteúdo (embeds, anexos apenas)
        if (!message.content && (!message.attachments || message.attachments.size === 0)) return;

        try {
            // Obter configuração do servidor do cache
            const guildConfig = client.guildConfigs?.get(message.guild.id);

            // Verificar se logs estão habilitados (priorizar logs_enabled do módulo de logs)
            const logsEnabled = guildConfig?.logs_enabled || guildConfig?.log_enabled;
            if (!logsEnabled) {
                console.log(`📝 [MESSAGE DELETE] Logs desabilitados para ${message.guild.name} (logs_enabled: ${guildConfig?.logs_enabled}, log_enabled: ${guildConfig?.log_enabled})`);
                return;
            }

            // Verificar se canal de logs está configurado (priorizar automod_log_channel para auto-moderação)
            const logChannelId = guildConfig?.automod_log_channel || guildConfig?.general_log_channel || guildConfig?.log_channel_id || guildConfig?.log_channel;

            if (!logChannelId) {
                console.log(`📝 [MESSAGE DELETE] Canal de logs não configurado para ${message.guild.name}`);
                return;
            }

            const logChannel = message.guild.channels.cache.get(logChannelId);

            if (!logChannel) {
                console.log(`📝 [MESSAGE DELETE] Canal de logs não encontrado: ${logChannelId}`);
                return;
            }

            console.log(`📝 [MESSAGE DELETE] Enviando log para #${logChannel.name} em ${message.guild.name}`);

            // Criar embed de log
            const deleteEmbed = new EmbedBuilder()
                .setColor('#ff4757')
                .setTitle(`🗑️ Mensagem Deletada - ${message.author.username}`)
                .setTimestamp()
                .setFooter({ text: 'Nodex | Moderação - Sistema de Logs' });

            // Informações do usuário (agora garantimos que existe)
            deleteEmbed.addFields(
                { name: '👤 Usuário', value: `${message.author.tag} (${message.author.id})`, inline: true },
                { name: '📺 Canal', value: `${message.channel} (${message.channel.name})`, inline: true },
                { name: '📅 Data', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            );

            // Avatar do usuário
            deleteEmbed.setThumbnail(message.author.displayAvatarURL());

            // Conteúdo da mensagem
            if (message.content && message.content.trim().length > 0) {
                const content = message.content.length > 1000
                    ? message.content.substring(0, 997) + '...'
                    : message.content;

                deleteEmbed.addFields({
                    name: '📝 Conteúdo Original',
                    value: `\`\`\`${content}\`\`\``,
                    inline: false
                });
            } else if (message.attachments && message.attachments.size > 0) {
                deleteEmbed.addFields({
                    name: '📝 Conteúdo',
                    value: '*Mensagem continha apenas anexos*',
                    inline: false
                });
            } else {
                deleteEmbed.addFields({
                    name: '📝 Conteúdo',
                    value: '*Conteúdo vazio ou não disponível*',
                    inline: false
                });
            }

            // Anexos (se houver)
            if (message.attachments && message.attachments.size > 0) {
                const attachments = message.attachments.map(att => `[${att.name}](${att.url})`).join('\n');
                deleteEmbed.addFields({
                    name: '📎 Anexos',
                    value: attachments,
                    inline: false
                });
            }

            // Embeds (se houver)
            if (message.embeds && message.embeds.length > 0) {
                deleteEmbed.addFields({
                    name: '🔗 Embeds',
                    value: `${message.embeds.length} embed(s) anexado(s)`,
                    inline: true
                });
            }

            // ID da mensagem para referência
            deleteEmbed.addFields({
                name: '🆔 ID da Mensagem',
                value: `\`${message.id}\``,
                inline: true
            });

            await logChannel.send({ embeds: [deleteEmbed] });

            client.logger.info(`Mensagem deletada logada: ${message.author?.tag} em #${message.channel?.name}`);

        } catch (error) {
            client.logger.error('Erro ao logar mensagem deletada:', error);
        }
    }
};
