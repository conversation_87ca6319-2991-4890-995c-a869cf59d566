/**
 * ========================================
 * COMANDO PARA CORRIGIR CONFIGURAÇÃO DE LOGS
 * Comando para diagnosticar e corrigir problemas de logs
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('fix-logs')
        .setDescription('🔧 Diagnosticar e corrigir problemas de logs')
        .addBooleanOption(option =>
            option.setName('habilitar')
                .setDescription('Forçar habilitação dos logs')
                .setRequired(false))
        .addChannelOption(option =>
            option.setName('canal_geral')
                .setDescription('Definir canal geral de logs')
                .setRequired(false))
        .addChannelOption(option =>
            option.setName('canal_automod')
                .setDescription('Definir canal de auto-moderação')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false),

    async execute(interaction) {
        const client = interaction.client;
        const guild = interaction.guild;
        const habilitarLogs = interaction.options.getBoolean('habilitar');
        const canalGeral = interaction.options.getChannel('canal_geral');
        const canalAutomod = interaction.options.getChannel('canal_automod');

        await interaction.deferReply({ ephemeral: true });

        try {
            // Buscar configuração atual do cache
            const guildConfig = client.guildConfigs?.get(guild.id);
            
            // Buscar configuração do banco de dados
            const dbConfig = await client.database.getGuildConfig(guild.id);

            console.log(`🔧 [FIX LOGS] Configuração do cache:`, {
                logs_enabled: guildConfig?.logs_enabled,
                log_enabled: guildConfig?.log_enabled,
                general_log_channel: guildConfig?.general_log_channel,
                automod_log_channel: guildConfig?.automod_log_channel
            });

            console.log(`🔧 [FIX LOGS] Configuração do banco:`, {
                logs_enabled: dbConfig?.logs_enabled,
                log_enabled: dbConfig?.log_enabled,
                settings: typeof dbConfig?.settings === 'string' ? JSON.parse(dbConfig?.settings || '{}') : dbConfig?.settings
            });

            // Preparar configuração corrigida
            let configToSave = dbConfig || {
                prefix: '!',
                language: 'pt-BR',
                timezone: 'America/Sao_Paulo',
                settings: {}
            };

            // Parse settings se necessário
            let settings = {};
            if (configToSave.settings) {
                try {
                    if (typeof configToSave.settings === 'string') {
                        settings = JSON.parse(configToSave.settings);
                    } else {
                        settings = configToSave.settings;
                    }
                } catch (parseError) {
                    settings = {};
                }
            }

            // Aplicar correções
            let mudancas = [];

            if (habilitarLogs !== null) {
                settings.logs_enabled = habilitarLogs;
                settings.log_enabled = habilitarLogs; // Compatibilidade
                mudancas.push(`✅ Logs ${habilitarLogs ? 'habilitados' : 'desabilitados'}`);
            }

            if (canalGeral) {
                settings.general_log_channel = canalGeral.id;
                mudancas.push(`✅ Canal geral definido: <#${canalGeral.id}>`);
            }

            if (canalAutomod) {
                settings.automod_log_channel = canalAutomod.id;
                mudancas.push(`✅ Canal auto-moderação definido: <#${canalAutomod.id}>`);
            }

            // Se nenhuma mudança específica foi solicitada, apenas diagnosticar
            if (mudancas.length === 0) {
                // Diagnóstico completo
                const diagnosticEmbed = new EmbedBuilder()
                    .setTitle('🔍 Diagnóstico de Logs')
                    .setDescription('Estado atual do sistema de logs')
                    .setColor('#3498db')
                    .addFields(
                        {
                            name: '📊 Cache do Bot',
                            value: `**logs_enabled:** \`${guildConfig?.logs_enabled}\`\n**log_enabled:** \`${guildConfig?.log_enabled}\`\n**general_log_channel:** \`${guildConfig?.general_log_channel}\`\n**automod_log_channel:** \`${guildConfig?.automod_log_channel}\``,
                            inline: false
                        },
                        {
                            name: '💾 Banco de Dados',
                            value: `**logs_enabled:** \`${settings.logs_enabled}\`\n**log_enabled:** \`${settings.log_enabled}\`\n**general_log_channel:** \`${settings.general_log_channel}\`\n**automod_log_channel:** \`${settings.automod_log_channel}\``,
                            inline: false
                        },
                        {
                            name: '🔧 Comandos de Correção',
                            value: `\`/fix-logs habilitar:True\` - Habilitar logs\n\`/fix-logs canal_geral:#logs\` - Definir canal geral\n\`/fix-logs canal_automod:#auto-mod\` - Definir canal auto-mod`,
                            inline: false
                        }
                    )
                    .setFooter({ text: 'Nodex | Moderação - Diagnóstico' })
                    .setTimestamp();

                // Verificar problemas comuns
                const problemas = [];
                if (!settings.logs_enabled && !settings.log_enabled) {
                    problemas.push('❌ Logs desabilitados');
                }
                if (!settings.general_log_channel) {
                    problemas.push('⚠️ Canal geral não configurado');
                }
                if (!settings.automod_log_channel) {
                    problemas.push('⚠️ Canal auto-moderação não configurado');
                }

                if (problemas.length > 0) {
                    diagnosticEmbed.addFields({
                        name: '⚠️ Problemas Detectados',
                        value: problemas.join('\n'),
                        inline: false
                    });
                }

                await interaction.editReply({ embeds: [diagnosticEmbed] });
                return;
            }

            // Salvar configuração corrigida
            configToSave.settings = settings;
            await client.database.saveGuildConfig(guild.id, configToSave);

            // Atualizar cache
            if (client.guildConfigs) {
                client.guildConfigs.set(guild.id, {
                    ...guildConfig,
                    ...settings
                });
            }

            // Emitir evento de atualização
            client.emit('configUpdated', { guildId: guild.id, config: settings, section: 'logs' });

            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Logs Corrigidos!')
                .setDescription('Configuração de logs foi corrigida com sucesso.')
                .addFields({
                    name: '🔧 Mudanças Aplicadas',
                    value: mudancas.join('\n'),
                    inline: false
                })
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Correção' })
                .setTimestamp();

            await interaction.editReply({ embeds: [successEmbed] });

            console.log(`✅ [FIX LOGS] Configuração corrigida para ${guild.name}:`, mudancas);

        } catch (error) {
            console.error('❌ [FIX LOGS] Erro:', error);

            await interaction.editReply({
                content: `❌ Erro ao corrigir logs:\n\`\`\`${error.message}\`\`\``
            });
        }
    }
};
