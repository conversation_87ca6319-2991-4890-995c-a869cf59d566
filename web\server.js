/**
 * ========================================
 * SERVIDOR WEB - DASHBOARD COMPLETO
 * Interface web avançada para o Nova Moderação Bot
 * ========================================
 */

const express = require('express');
const path = require('path');
const session = require('express-session');
const passport = require('passport');
const cors = require('cors');
const DiscordAuth = require('./auth/discord');
const SecurityMiddleware = require('./middleware/security');
const ValidationMiddleware = require('./middleware/validation');
const ErrorHandler = require('./middleware/errorHandler');
const SecureDatabaseManager = require('../database/SecureDatabaseManager');

class WebServer {
    constructor(client) {
        this.client = client;
        this.app = express();
        this.port = process.env.WEB_PORT || 3000;

        this.securityMiddleware = new SecurityMiddleware();
        this.validationMiddleware = new ValidationMiddleware();
        this.errorHandler = new ErrorHandler();
        this.secureDb = new SecureDatabaseManager();
        this.discordAuth = new DiscordAuth();
        global.client = client;

        if (client.database) {
            this.secureDb.initialize(client.database.db);
        }

        this.setupMiddleware();
        this.setupAuthRoutes();
        this.setupRoutes();
        
        // Rotas adicionais para páginas comuns
        this.app.get('/docs', (req, res) => {
            res.render('docs', {
                title: 'Documentação - Nodex | Moderação',
                botName: this.client.user?.username || 'Nodex'
            });
        });

        this.app.get('/support', (req, res) => {
            res.render('support', {
                title: 'Suporte - Nodex | Moderação',
                botName: this.client.user?.username || 'Nodex'
            });
        });

        this.app.get('/privacy', (req, res) => {
            res.render('privacy', {
                title: 'Política de Privacidade - Nodex | Moderação',
                botName: this.client.user?.username || 'Nodex'
            });
        });

        this.app.get('/terms', (req, res) => {
            res.render('terms', {
                title: 'Termos de Uso - Nodex | Moderação',
                botName: this.client.user?.username || 'Nodex'
            });
        });

        // API para estatísticas
        this.app.get('/api/stats', (req, res) => {
            res.json({
                servers: this.client.guilds.cache.size,
                users: this.client.guilds.cache.reduce((a, g) => a + g.memberCount, 0),
                timestamp: new Date().toISOString()
            });
        });

        this.setupErrorHandling();
    }

    setupMiddleware() {
        this.app.use(this.securityMiddleware.getHelmetConfig());
        this.app.use(cors({
            origin: process.env.NODE_ENV === 'production'
                ? [process.env.FRONTEND_URL]
                : ['http://localhost:3000', 'http://127.0.0.1:3000'],
            credentials: true,
            optionsSuccessStatus: 200
        }));
        this.app.use(this.securityMiddleware.generalRateLimit);
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
        this.app.use(this.validationMiddleware.sanitizeXSS);

        this.app.set('view engine', 'ejs');
        this.app.set('views', path.join(__dirname, 'views'));
        this.app.use(express.static(path.join(__dirname, 'public')));

        const sessionSecret = process.env.SESSION_SECRET;
        if (!sessionSecret || sessionSecret === 'nodex-secret-key') {
            console.error('❌ [SECURITY] SESSION_SECRET não configurado ou usando valor padrão!');
            process.exit(1);
        }

        // CORREÇÃO CRÍTICA 7.4: Configurações de sessão seguras
        this.app.use(session({
            secret: sessionSecret,
            resave: false,
            saveUninitialized: false,
            name: 'nodex.sid',
            cookie: {
                maxAge: 24 * 60 * 60 * 1000, // 24 horas
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
                // Regenerar ID da sessão periodicamente
                path: '/',
                domain: process.env.NODE_ENV === 'production' ? process.env.DOMAIN : undefined
            },
            rolling: true,
            // Regenerar sessão em login para prevenir session fixation
            genid: () => {
                return require('crypto').randomBytes(32).toString('hex');
            }
        }));

        this.app.use(passport.initialize());
        this.app.use(passport.session());

        // CORREÇÃO CRÍTICA 7.1: Aplicar proteção CSRF
        this.app.use(this.securityMiddleware.csrfProtection);

        if (process.env.CLIENT_ID && process.env.DISCORD_CLIENT_SECRET && process.env.DISCORD_REDIRECT_URI) {
            const DiscordStrategy = require('passport-discord').Strategy;
            passport.use(new DiscordStrategy({
                clientID: process.env.CLIENT_ID,
                clientSecret: process.env.DISCORD_CLIENT_SECRET,
                callbackURL: process.env.DISCORD_REDIRECT_URI,
                scope: ['identify', 'guilds']
            }, (accessToken, refreshToken, profile, done) => {
                console.log(`🔐 [OAUTH] Usuário autenticado via Discord: ${profile.username}#${profile.discriminator} (${profile.id})`);
                return done(null, profile);
            }));

            passport.serializeUser((user, done) => {
                console.log(`🔐 [SESSION] Serializando usuário: ${user.username}`);
                done(null, user);
            });

            passport.deserializeUser((obj, done) => {
                console.log(`🔐 [SESSION] Deserializando usuário: ${obj.username}`);
                done(null, obj);
            });
        } else {
            console.warn('⚠️ [PASSPORT] Credenciais OAuth2 não encontradas, autenticação desabilitada');
        }

        this.app.use((req, res, next) => {
            res.locals.user = req.user || null;
            // CORREÇÃO CRÍTICA 7.1: Disponibilizar token CSRF para views
            res.locals.csrfToken = req.csrfToken ? req.csrfToken() : null;
            next();
        });
    }

    setupAuthRoutes() {
        this.app.get('/login', (req, res) => {
            if (req.isAuthenticated()) return res.redirect('/dashboard');
            res.render('login', { title: 'Login', botName: this.client.user?.username || 'Nodex' });
        });

        this.app.get('/auth/discord', passport.authenticate('discord'));

        this.app.get('/auth/discord/callback', passport.authenticate('discord', {
            failureRedirect: '/login'
        }), (req, res) => {
            res.redirect('/dashboard');
        });

        this.app.get('/logout', (req, res) => {
            req.logout(err => {
                if (err) { console.error(err); }
                res.redirect('/');
            });
        });
    }

    setupRoutes() {
        // Rota principal
        this.app.get('/', (req, res) => {
            console.log('📍 [ROUTE] Acessando rota principal /');
            try {
                res.render('index', {
                    title: 'Nodex | Moderação',
                    botName: this.client.user?.username || 'Nodex',
                    serverCount: this.client.guilds.cache.size || 0,
                    userCount: this.client.guilds.cache.reduce((a, g) => a + g.memberCount, 0) || 0
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na rota principal:', error);
                res.status(500).render('error', { 
                    title: 'Erro Interno - Nodex | Moderação',
                    error: {
                        status: 500,
                        message: 'Erro interno do servidor.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // Dashboard
        this.app.get('/dashboard', async (req, res) => {
            console.log('📍 [ROUTE] Acessando dashboard');
            
            try {
                // Verificar autenticação de forma mais robusta
                let user = null;
                let guilds = [];
                
                if (req.isAuthenticated && req.isAuthenticated() && req.user) {
                    console.log('✅ [DASHBOARD] Usuário autenticado:', req.user.username);
                    user = req.user;
                    
                    try {
                        guilds = await this.getAdminGuilds(req.user);
                        console.log(`✅ [DASHBOARD] ${guilds.length} guilds carregadas`);
                    } catch (guildError) {
                        console.error('❌ [DASHBOARD] Erro ao carregar guilds:', guildError.message);
                        guilds = [];
                    }
                } else {
                    console.log('⚠️ [DASHBOARD] Usuário não autenticado, redirecionando...');
                    return res.redirect('/login');
                }

                // Renderizar dashboard com dados seguros
                const renderData = {
                    title: 'Dashboard - Nodex | Moderação',
                    botName: this.client?.user?.username || 'Nodex',
                    user: user,
                    guilds: guilds,
                    client: {
                        user: this.client?.user || { id: 'unknown', username: 'Nodex' },
                        guilds: {
                            cache: {
                                size: this.client?.guilds?.cache?.size || 0
                            }
                        }
                    }
                };

                console.log('✅ [DASHBOARD] Renderizando dashboard com dados válidos');
                res.render('dashboard', renderData);
                
            } catch (error) {
                console.error('❌ [DASHBOARD] Erro crítico ao carregar dashboard:', error);
                
                // Tentar renderizar página de erro
                try {
                    res.status(500).render('error', { 
                        title: 'Erro no Dashboard - Nodex | Moderação',
                        error: {
                            status: 500,
                            message: 'Não foi possível carregar o dashboard.',
                            details: process.env.NODE_ENV === 'development' ? error.message : null
                        }
                    });
                } catch (renderError) {
                    console.error('❌ [DASHBOARD] Erro ao renderizar página de erro:', renderError);
                    res.status(500).json({
                        error: 'Erro crítico no dashboard',
                        message: 'Não foi possível carregar a interface',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    });
                }
            }
        });

        // Rotas adicionais para páginas comuns
        this.app.get('/docs', (req, res) => {
            console.log('📍 [ROUTE] Acessando documentação');
            try {
                res.render('docs', {
                    title: 'Documentação - Nodex | Moderação',
                    botName: this.client.user?.username || 'Nodex'
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na documentação:', error);
                res.status(500).render('error', { 
                    title: 'Erro na Documentação - Nodex | Moderação',
                    error: {
                        status: 500,
                        message: 'Erro ao carregar a documentação.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        this.app.get('/support', (req, res) => {
            console.log('📍 [ROUTE] Acessando suporte');
            try {
                res.render('support', {
                    title: 'Suporte - Nodex | Moderação',
                    botName: this.client.user?.username || 'Nodex'
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro no suporte:', error);
                res.status(500).render('error', { 
                    title: 'Erro no Suporte - Nodex | Moderação',
                    error: {
                        status: 500,
                        message: 'Erro ao carregar a página de suporte.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        this.app.get('/privacy', (req, res) => {
            console.log('📍 [ROUTE] Acessando política de privacidade');
            try {
                res.render('privacy', {
                    title: 'Política de Privacidade - Nodex | Moderação',
                    botName: this.client.user?.username || 'Nodex'
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro na privacidade:', error);
                res.status(500).render('error', { 
                    title: 'Erro na Privacidade - Nodex | Moderação',
                    error: {
                        status: 500,
                        message: 'Erro ao carregar a política de privacidade.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        this.app.get('/terms', (req, res) => {
            console.log('📍 [ROUTE] Acessando termos de uso');
            try {
                res.render('terms', {
                    title: 'Termos de Uso - Nodex | Moderação',
                    botName: this.client.user?.username || 'Nodex'
                });
            } catch (error) {
                console.error('❌ [ROUTE] Erro nos termos:', error);
                res.status(500).render('error', { 
                    title: 'Erro nos Termos - Nodex | Moderação',
                    error: {
                        status: 500,
                        message: 'Erro ao carregar os termos de uso.',
                        details: process.env.NODE_ENV === 'development' ? error.message : null
                    }
                });
            }
        });

        // API para estatísticas
        this.app.get('/api/stats', (req, res) => {
            console.log('📍 [API] Acessando estatísticas');
            try {
                res.json({
                    servers: this.client.guilds.cache.size || 0,
                    users: this.client.guilds.cache.reduce((a, g) => a + g.memberCount, 0) || 0,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                console.error('❌ [API] Erro nas estatísticas:', error);
                res.status(500).json({
                    error: 'Erro ao carregar estatísticas',
                    status: 500
                });
            }
        });

        // Rota de teste para verificar se o servidor está funcionando
        this.app.get('/test', (req, res) => {
            console.log('📍 [TEST] Rota de teste acessada');
            res.json({ 
                message: 'Servidor funcionando!', 
                timestamp: new Date().toISOString(),
                routes: 'OK'
            });
        });
    }

    setupErrorHandling() {
        this.app.use(this.errorHandler.notFoundHandler);
        this.app.use(this.errorHandler.errorHandler);
    }

    requireAuth(req, res, next) {
        console.log(`[requireAuth] Verificando autenticação para a rota: ${req.path}`);
        
        try {
            if (req.isAuthenticated && req.isAuthenticated()) {
                if (req.user && req.user.username) {
                    console.log(`[requireAuth] ✅ Usuário ${req.user.username} autenticado. Prosseguindo.`);
                    return next();
                } else {
                    console.log('[requireAuth] ⚠️ Usuário autenticado mas dados incompletos.');
                    return res.redirect('/login');
                }
            }
            
            console.log('[requireAuth] ❌ Usuário não autenticado. Redirecionando para /login.');
            res.redirect('/login');
            
        } catch (error) {
            console.error('[requireAuth] ❌ Erro na verificação de autenticação:', error);
            res.redirect('/login');
        }
    }

    async getAdminGuilds(user) {
        console.log('🔧 [getAdminGuilds] Iniciando busca de guilds administrativas...');
        
        try {
            // Verificar se o usuário existe e tem dados válidos
            if (!user) {
                console.warn('⚠️ [getAdminGuilds] Usuário não fornecido.');
                return [];
            }

            if (!user.guilds || !Array.isArray(user.guilds)) {
                console.warn('⚠️ [getAdminGuilds] Usuário não tem guilds ou o formato é inválido.');
                return [];
            }

            console.log(`🔍 [getAdminGuilds] Encontradas ${user.guilds.length} guilds no perfil do usuário.`);
            const adminGuilds = [];

            for (const userGuild of user.guilds) {
                try {
                    // Verificar se a guild tem dados válidos
                    if (!userGuild || !userGuild.id || !userGuild.name) {
                        console.warn(`⚠️ [getAdminGuilds] Guild inválida encontrada:`, userGuild);
                        continue;
                    }

                    // Verificar permissões de administrador
                    const permissions = userGuild.permissions || '0';
                    const hasAdmin = (parseInt(permissions) & 0x8) === 0x8;
                    
                    if (hasAdmin) {
                        // Verificar se o bot está na guild (com fallback)
                        let botInGuild = false;
                        try {
                            botInGuild = this.client && this.client.guilds && this.client.guilds.cache 
                                ? this.client.guilds.cache.has(userGuild.id) 
                                : false;
                        } catch (clientError) {
                            console.warn(`⚠️ [getAdminGuilds] Erro ao verificar bot na guild ${userGuild.name}:`, clientError.message);
                            botInGuild = false;
                        }

                        // Criar dados da guild
                        const guildData = {
                            id: userGuild.id,
                            name: userGuild.name,
                            icon: userGuild.icon || null,
                            permissions: permissions,
                            botInGuild: botInGuild,
                            iconURL: userGuild.icon 
                                ? `https://cdn.discordapp.com/icons/${userGuild.id}/${userGuild.icon}.png` 
                                : '/img/logo.png'
                        };
                        
                        adminGuilds.push(guildData);
                        console.log(`✅ [getAdminGuilds] Guild qualificada: ${userGuild.name} (Bot: ${botInGuild ? 'Sim' : 'Não'})`);
                    } else {
                        console.log(`ℹ️ [getAdminGuilds] Guild ${userGuild.name}: usuário não tem permissão de admin`);
                    }
                } catch (guildError) {
                    console.error(`❌ [getAdminGuilds] Erro ao processar a guild ${userGuild.name || userGuild.id}:`, guildError.message);
                    continue;
                }
            }

            console.log(`✅ [getAdminGuilds] Busca finalizada. Total de guilds qualificadas: ${adminGuilds.length}`);
            return adminGuilds;
            
        } catch (error) {
            console.error('❌ [getAdminGuilds] Erro crítico na função:', error);
            return [];
        }
    }

    start() {
        this.app.listen(this.port, () => {
            console.log(`✅ [WEB] Servidor web rodando em http://localhost:${this.port}`);
        });
    }
}

module.exports = WebServer;
