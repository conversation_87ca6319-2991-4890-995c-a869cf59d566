/* ========================================
   DASHBOARD ESPECÍFICO - CONFIGURAÇÕES
   ======================================== */

/* Layout do Dashboard */
.dashboard-body {
    display: flex;
    min-height: 100vh;
    background: var(--gradient-dark);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--gradient-card);
    border-right: 1px solid rgba(0, 255, 127, 0.1);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 255, 127, 0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
}

.sidebar-brand i {
    color: var(--primary-green);
    font-size: 1.3rem;
}

.sidebar-content {
    flex: 1;
    padding: 1.5rem 0;
}

.server-info {
    padding: 0 1.5rem 1.5rem;
    border-bottom: 1px solid rgba(0, 255, 127, 0.1);
    margin-bottom: 1.5rem;
}

.server-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
    border: 2px solid rgba(0, 255, 127, 0.2);
}

.server-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.server-avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--black);
}

.server-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.server-members {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Navegação da Sidebar */
.sidebar-nav {
    padding: 0 1rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background: rgba(0, 255, 127, 0.1);
    color: var(--primary-green);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: var(--black);
    font-weight: 600;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: var(--primary-green);
    border-radius: 2px;
}

.nav-item i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(0, 255, 127, 0.1);
}

/* Conteúdo Principal */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    min-height: 100vh;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 255, 127, 0.1);
}

.header-info h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.header-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Seções de Configuração */
.config-section {
    display: none;
    animation: slideInUp 0.5s ease-out;
}

.config-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.section-title i {
    color: var(--primary-green);
}

.section-description {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Grid de Configurações - Otimizado para UX Comercial */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.25rem;
    margin-top: 1rem;
}

/* Cards de Configuração - Design Comercial Compacto */
.config-card {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(20, 20, 20, 0.98));
    border: 1px solid rgba(0, 255, 127, 0.08);
    border-radius: 12px;
    padding: 1.25rem;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.config-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-green), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.config-card:hover {
    border-color: rgba(0, 255, 127, 0.25);
    box-shadow: 0 8px 32px rgba(0, 255, 127, 0.08);
    transform: translateY(-1px);
}

.config-card:hover::before {
    opacity: 1;
}

/* Header Compacto com Hierarquia Visual Clara */
.config-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
    gap: 1rem;
}

.config-info {
    flex: 1;
    min-width: 0;
}

.config-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.config-title-icon {
    font-size: 0.9rem;
    color: var(--primary-green);
    opacity: 0.8;
}

.config-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0;
    opacity: 0.9;
}

/* Status Badge Profissional */
.config-status {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    white-space: nowrap;
    margin-top: 0.5rem;
}

.config-status.enabled {
    background: rgba(0, 255, 127, 0.1);
    color: var(--primary-green);
    border: 1px solid rgba(0, 255, 127, 0.2);
}

.config-status.disabled {
    background: rgba(128, 128, 128, 0.1);
    color: #888;
    border: 1px solid rgba(128, 128, 128, 0.2);
}

.config-status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Formulários */
.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-group input,
.input-group select,
.input-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: var(--dark-gray);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(0, 255, 127, 0.1);
}

/* Conteúdo do Card - Layout Otimizado */
.config-content {
    margin-top: 0.75rem;
}

/* Toggle Group - Design Comercial Premium */
.toggle-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    padding: 0.75rem 0;
}

.toggle-group.primary {
    background: rgba(0, 255, 127, 0.03);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(0, 255, 127, 0.08);
}

/* Toggle Switch Premium */
.toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
    flex-shrink: 0;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(128, 128, 128, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 22px;
    border: 1px solid rgba(128, 128, 128, 0.2);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle input:checked + .toggle-slider {
    background: linear-gradient(135deg, var(--primary-green), #00cc66);
    border-color: var(--primary-green);
    box-shadow: 0 0 8px rgba(0, 255, 127, 0.3);
}

.toggle input:checked + .toggle-slider:before {
    transform: translateX(22px);
    background: #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.toggle:hover .toggle-slider {
    box-shadow: 0 0 0 3px rgba(0, 255, 127, 0.1);
}

/* Toggle Label Otimizado */
.toggle-label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
    flex: 1;
    margin: 0;
}

.toggle-label.primary {
    font-weight: 600;
    color: var(--text-primary);
}

/* Sub-configurações */
.config-subsection {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 255, 127, 0.08);
}

.config-subsection.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.subsection-title {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading e Toasts */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
}

.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 0.75rem;
    z-index: 10000;
    animation: slideInLeft 0.3s ease-out;
}

.toast.show {
    display: flex;
}

.toast-success {
    background: linear-gradient(135deg, #00ff7f, #0d4d20);
    border: 1px solid rgba(0, 255, 127, 0.3);
}

.toast-error {
    background: linear-gradient(135deg, #ff4757, #c44569);
    border: 1px solid rgba(255, 71, 87, 0.3);
}

/* Input Groups Otimizados */
.input-group.compact {
    margin-bottom: 0.75rem;
}

.input-group.compact label {
    font-size: 0.8rem;
    margin-bottom: 0.375rem;
}

.input-group.compact input,
.input-group.compact select {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
}

/* Cards Especiais */
.config-card.featured {
    border: 1px solid rgba(0, 255, 127, 0.2);
    background: linear-gradient(145deg,
        rgba(0, 255, 127, 0.02),
        rgba(26, 26, 26, 0.98));
}

.config-card.featured .config-title {
    color: var(--primary-green);
}

.config-card.warning {
    border-color: rgba(255, 193, 7, 0.2);
    background: linear-gradient(145deg,
        rgba(255, 193, 7, 0.02),
        rgba(26, 26, 26, 0.98));
}

.config-card.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* Responsividade Otimizada */
@media (max-width: 1200px) {
    .config-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .config-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    }

    .config-card {
        padding: 1rem;
    }

    .config-header {
        margin-bottom: 0.75rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-actions {
        justify-content: stretch;
    }

    .header-actions .btn {
        flex: 1;
        justify-content: center;
    }

    .config-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .config-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .toggle-group {
        padding: 0.5rem 0;
    }
}

@media (max-width: 480px) {
    .config-card {
        padding: 0.875rem;
        border-radius: 8px;
    }

    .config-title {
        font-size: 0.95rem;
    }

    .config-description {
        font-size: 0.8rem;
    }

    .toggle {
        width: 40px;
        height: 20px;
    }

    .toggle-slider:before {
        height: 14px;
        width: 14px;
    }

    .toggle input:checked + .toggle-slider:before {
        transform: translateX(20px);
    }
}
