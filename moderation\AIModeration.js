/**
 * ========================================
 * SISTEMA DE MODERAÇÃO POR IA
 * Análise inteligente de conteúdo usando Nodex | API
 * ========================================
 */

const axios = require('axios');

class AIModeration {
    constructor(client) {
        this.client = client;
        this.apiKey = process.env.OPENROUTER_API_KEY || process.env.NODEX_API_KEY;
        this.provider = process.env.AI_PROVIDER || 'openrouter';
        this.model = process.env.AI_MODEL || 'deepseek/deepseek-chat-v3-0324:free';
        this.apiUrl = process.env.AI_BASE_URL ? `${process.env.AI_BASE_URL}/chat/completions` : 'https://openrouter.ai/api/v1/chat/completions';
        this.enabled = process.env.AI_ENABLED === 'true' && !!this.apiKey;
        
        // Cache para evitar análises repetidas
        this.analysisCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
        
        // Configurações de moderação (rigoroso com ataques pessoais e racismo)
        this.config = {
            toxicityThreshold: 0.6,   // MAIS BAIXO - ataques pessoais devem ser punidos
            spamThreshold: 0.7,       // Spam continua sendo moderado
            harassmentThreshold: 0.6, // Assédio continua sendo moderado
            hateSpeechThreshold: 0.4, // BAIXO - racismo deve ser punido rapidamente
            violenceThreshold: 0.4,   // Ameaças de violência
            maxMessageLength: 2000,
            rateLimitPerMinute: 60
        };

        // CORREÇÃO CRÍTICA 4.2: Rate limiting inteligente
        this.requestCounts = new Map();
        this.rateLimiter = {
            global: { requests: [], maxPerMinute: 60, maxPerHour: 1000 },
            perGuild: new Map(), // guildId -> { requests: [], quota: number }
            quotaLimits: {
                small: 10,   // Servidores pequenos (< 100 membros)
                medium: 30,  // Servidores médios (100-1000 membros)
                large: 60,   // Servidores grandes (> 1000 membros)
                premium: 120 // Servidores premium (se implementado)
            }
        };

        if (this.enabled) {
            this.client.logger.info(`🧠 Sistema de IA para moderação inicializado (${this.provider} - ${this.model})`);
            this.startCleanupInterval();
            this.setupIntelligentRateLimiting();
        } else {
            this.client.logger.warn('⚠️ Sistema de IA desabilitado - API_KEY não encontrada ou AI_ENABLED=false');
        }

        // Listener para mudanças de configuração
        this.client.on('guildConfigChanged', (guildId, config) => {
            console.log(`🔄 [IA] Configurações atualizadas para servidor ${guildId}`);
            // Limpar cache de configurações se necessário
            if (this.client.guildConfigs) {
                this.client.guildConfigs.delete(guildId);
            }
        });
    }

    /**
     * Analisa uma mensagem usando IA
     */
    async analyzeMessage(message, guildId) {
        if (!this.enabled) return null;
        
        try {
            // Verificar rate limit
            if (!this.checkRateLimit(guildId)) {
                this.client.logger.warn(`Rate limit excedido para servidor ${guildId}`);
                return null;
            }

            // Verificar cache
            const cacheKey = this.generateCacheKey(message.content);
            if (this.analysisCache.has(cacheKey)) {
                const cached = this.analysisCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return cached.result;
                }
                this.analysisCache.delete(cacheKey);
            }

            // Preparar prompt para análise
            const prompt = this.buildAnalysisPrompt(message.content);
            
            // Fazer requisição para a API
            const headers = {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            };

            // Adicionar headers específicos do OpenRouter se necessário
            if (this.provider === 'openrouter') {
                headers['HTTP-Referer'] = 'https://discord-bot.local';
                headers['X-Title'] = 'Nodex Moderação Bot';
            }

            const response = await axios.post(this.apiUrl, {
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'Você é um moderador de Discord. REGRAS: 1) Palavrões isolados (porra, caralho) = score baixo. 2) ATAQUES PESSOAIS DIRETOS ("você é um merda", "vai se fuder") = toxicity ALTO (0.7+). 3) RACISMO = hate_speech 0.9+. 4) Contexto importa: xingar pessoa específica é GRAVE, palavrão solto é normal.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 500,
                temperature: 0.1
            }, {
                headers,
                timeout: 15000
            });

            // Processar resposta
            const analysis = this.parseAIResponse(response.data.choices[0].message.content);
            
            // Salvar no cache
            this.analysisCache.set(cacheKey, {
                result: analysis,
                timestamp: Date.now()
            });

            // Incrementar contador de rate limit
            this.incrementRateLimit(guildId);

            return analysis;

        } catch (error) {
            // CORREÇÃO CRÍTICA 4.1: Sanitizar logs de API para remover chaves sensíveis
            const sanitizedError = this.sanitizeAPIError(error);
            this.client.logger.error('Erro na análise de IA:', sanitizedError);
            return null;
        }
    }

    /**
     * Constrói o prompt para análise
     */
    buildAnalysisPrompt(content) {
        return `
Analise a seguinte mensagem do Discord e retorne um JSON com scores de 0 a 1:

Mensagem: "${content}"

Retorne APENAS um JSON válido com esta estrutura:
{
    "toxicity": 0.0,
    "spam": 0.0,
    "harassment": 0.0,
    "hate_speech": 0.0,
    "sexual_content": 0.0,
    "violence": 0.0,
    "self_harm": 0.0,
    "overall_risk": 0.0,
    "action_recommended": "none|warn|timeout|kick|ban",
    "reason": "Breve explicação em português"
}

Critérios IMPORTANTES:
- toxicity: ATAQUES PESSOAIS DIRETOS = 0.7+ ("você é um merda", "vai se fuder", "seu idiota"). Palavrões isolados = score baixo (0.1-0.3)
- spam: Links suspeitos, mensagens repetitivas, promoções não solicitadas
- harassment: Assédio direto, bullying sistemático, perseguição
- hate_speech: CRÍTICO - Qualquer discriminação racial (preto, negro, macaco em contexto ofensivo) = 0.9+. Também homofobia, xenofobia, etc.
- sexual_content: Conteúdo sexual explícito (não palavrões casuais)
- violence: Ameaças reais de violência física
- self_harm: Incentivo real a autolesão ou suicídio
- overall_risk: Risco REAL da mensagem (0-1)

EXEMPLOS CRÍTICOS:
- "você é um merda" = toxicity 0.8+ (ataque pessoal direto)
- "vai se fuder" = toxicity 0.7+ (ofensa direta)
- "filho da puta preto" = hate_speech 0.9+ (racismo)
- "porra" = toxicity 0.2 (palavrão casual)
- "que merda" = toxicity 0.3 (expressão casual)

REGRA DE OURO: Se é direcionado a uma PESSOA específica com intenção ofensiva = PUNIR. Se é expressão casual = PERMITIR.
        `;
    }

    /**
     * Processa a resposta da IA
     */
    parseAIResponse(response) {
        try {
            // Tentar extrair JSON da resposta
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('JSON não encontrado na resposta');
            }

            const analysis = JSON.parse(jsonMatch[0]);
            
            // Validar estrutura
            const requiredFields = ['toxicity', 'spam', 'harassment', 'hate_speech', 'overall_risk'];
            for (const field of requiredFields) {
                if (typeof analysis[field] !== 'number') {
                    analysis[field] = 0;
                }
            }

            // Garantir que os valores estejam entre 0 e 1
            for (const key in analysis) {
                if (typeof analysis[key] === 'number') {
                    analysis[key] = Math.max(0, Math.min(1, analysis[key]));
                }
            }

            return analysis;

        } catch (error) {
            this.client.logger.error('Erro ao processar resposta da IA:', error);
            return {
                toxicity: 0,
                spam: 0,
                harassment: 0,
                hate_speech: 0,
                overall_risk: 0,
                action_recommended: 'none',
                reason: 'Erro na análise'
            };
        }
    }

    /**
     * Determina ação baseada na análise (com sistema de segunda chance)
     */
    determineAction(analysis, userHistory = null) {
        if (!analysis) return null;

        const { toxicity, spam, harassment, hate_speech, violence, overall_risk } = analysis;

        // Ações baseadas em thresholds mais restritivos
        if (hate_speech >= this.config.hateSpeechThreshold || violence >= 0.7 || overall_risk >= 0.9) {
            return {
                action: 'ban',
                reason: 'Discurso de ódio ou ameaças graves detectadas pela IA',
                severity: 'high'
            };
        }

        // Sistema de segunda chance para toxicidade alta
        if (toxicity >= this.config.toxicityThreshold || (harassment >= this.config.harassmentThreshold && overall_risk >= 0.7)) {
            // Verificar se usuário já tem advertências recentes
            const recentWarnings = userHistory?.filter(action =>
                action.action === 'warn' &&
                action.automatic &&
                Date.now() - new Date(action.created_at).getTime() < 24 * 60 * 60 * 1000 // 24 horas
            ) || [];

            if (recentWarnings.length >= 1) {
                // Já foi advertido, aplicar timeout
                return {
                    action: 'timeout',
                    reason: 'Reincidência em comportamento tóxico após advertência',
                    severity: 'medium',
                    duration: 30 * 60 * 1000, // 30 minutos
                    isSecondChance: true
                };
            } else {
                // Primeira vez, dar advertência
                return {
                    action: 'warn',
                    reason: 'Ataque pessoal direto detectado pela IA',
                    severity: 'medium',
                    isFirstChance: true
                };
            }
        }

        if (spam >= this.config.spamThreshold) {
            return {
                action: 'delete',
                reason: 'Spam detectado pela IA',
                severity: 'low'
            };
        }

        // Advertências para casos moderados
        if ((harassment >= 0.8 || violence >= 0.5) && overall_risk >= 0.6) {
            return {
                action: 'warn',
                reason: 'Comportamento potencialmente problemático detectado pela IA',
                severity: 'low'
            };
        }

        return null; // Não fazer nada para palavrões casuais
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Verificar rate limit inteligente
     */
    checkRateLimit(guildId) {
        const now = Date.now();

        // Verificar rate limit global
        if (!this.checkGlobalRateLimit(now)) {
            this.client.logger.warn('🚨 [IA] Rate limit global excedido');
            return false;
        }

        // Verificar rate limit por servidor
        if (!this.checkGuildRateLimit(guildId, now)) {
            this.client.logger.warn(`🚨 [IA] Rate limit excedido para servidor ${guildId}`);
            return false;
        }

        return true;
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Incrementar contador de rate limit inteligente
     */
    incrementRateLimit(guildId) {
        const now = Date.now();

        // Incrementar contador global
        this.rateLimiter.global.requests.push(now);

        // Incrementar contador por servidor
        if (!this.rateLimiter.perGuild.has(guildId)) {
            this.initializeGuildRateLimit(guildId);
        }

        const guildLimiter = this.rateLimiter.perGuild.get(guildId);
        guildLimiter.requests.push(now);

        // Manter compatibilidade com sistema antigo
        const minute = Math.floor(now / 60000);
        const key = `${guildId}-${minute}`;
        const count = this.requestCounts.get(key) || 0;
        this.requestCounts.set(key, count + 1);
    }

    /**
     * Gera chave de cache para mensagem
     */
    generateCacheKey(content) {
        // Hash simples do conteúdo
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Converter para 32bit
        }
        return hash.toString();
    }

    /**
     * Inicia limpeza periódica de cache e rate limits
     */
    startCleanupInterval() {
        setInterval(() => {
            this.cleanupCache();
            this.cleanupRateLimits();
        }, 5 * 60 * 1000); // A cada 5 minutos
    }

    /**
     * Limpa cache expirado
     */
    cleanupCache() {
        const now = Date.now();
        for (const [key, data] of this.analysisCache.entries()) {
            if (now - data.timestamp > this.cacheTimeout) {
                this.analysisCache.delete(key);
            }
        }
    }

    /**
     * Limpa rate limits antigos
     */
    cleanupRateLimits() {
        const now = Date.now();
        const currentMinute = Math.floor(now / 60000);
        
        for (const key of this.requestCounts.keys()) {
            const keyMinute = parseInt(key.split('-').pop());
            if (currentMinute - keyMinute > 2) { // Manter apenas últimos 2 minutos
                this.requestCounts.delete(key);
            }
        }
    }

    /**
     * Obtém estatísticas do sistema
     */
    getStats() {
        return {
            enabled: this.enabled,
            cacheSize: this.analysisCache.size,
            rateLimitEntries: this.requestCounts.size,
            config: this.config
        };
    }

    /**
     * Atualiza configurações
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.client.logger.info('Configurações de IA atualizadas:', newConfig);
    }

    /**
     * Atualiza configurações específicas de um servidor
     */
    async updateGuildConfig(guildId, guildConfig) {
        try {
            console.log(`🔧 [IA] Atualizando configurações para servidor ${guildId}:`, guildConfig);

            // Limpar cache de configurações para forçar reload
            if (this.client.guildConfigs) {
                this.client.guildConfigs.delete(guildId);
            }

            // Se há configurações específicas de IA, aplicar
            if (guildConfig.enabled !== undefined) {
                console.log(`🧠 [IA] Sistema de IA ${guildConfig.enabled ? 'ATIVADO' : 'DESATIVADO'} para servidor ${guildId}`);
            }

            if (guildConfig.sensitivity !== undefined) {
                console.log(`🎯 [IA] Sensibilidade atualizada para ${guildConfig.sensitivity} no servidor ${guildId}`);
            }

            // Emitir evento para outros sistemas
            this.client.emit('aiConfigUpdated', guildId, guildConfig);

            return { success: true };

        } catch (error) {
            console.error(`❌ [IA] Erro ao atualizar configurações do servidor ${guildId}:`, error);
            throw error;
        }
    }

    /**
     * Atualiza sensibilidade específica de um servidor
     */
    async updateSensitivity(guildId, sensitivity) {
        try {
            console.log(`🎯 [IA] Atualizando sensibilidade para ${sensitivity} no servidor ${guildId}`);

            // Limpar cache para forçar reload das configurações
            if (this.client.guildConfigs) {
                this.client.guildConfigs.delete(guildId);
            }

            return { success: true };

        } catch (error) {
            console.error(`❌ [IA] Erro ao atualizar sensibilidade do servidor ${guildId}:`, error);
            throw error;
        }
    }

    /**
     * CORREÇÃO CRÍTICA 4.1: Sanitizar erros de API para remover dados sensíveis
     */
    sanitizeAPIError(error) {
        if (!error) return error;

        // Criar cópia do erro para não modificar o original
        const sanitized = {
            message: error.message,
            name: error.name,
            code: error.code,
            status: error.status || error.response?.status
        };

        // Sanitizar mensagem de erro
        if (sanitized.message) {
            sanitized.message = this.sanitizeString(sanitized.message);
        }

        // Sanitizar dados de resposta se existirem
        if (error.response?.data) {
            sanitized.responseData = this.sanitizeObject(error.response.data);
        }

        // Sanitizar configuração da requisição (onde pode estar a API key)
        if (error.config) {
            sanitized.config = {
                method: error.config.method,
                url: this.sanitizeURL(error.config.url),
                timeout: error.config.timeout
            };
            // NÃO incluir headers que podem conter API keys
        }

        return sanitized;
    }

    /**
     * CORREÇÃO CRÍTICA 4.1: Sanitizar strings removendo dados sensíveis
     */
    sanitizeString(str) {
        if (!str || typeof str !== 'string') return str;

        // Padrões para detectar dados sensíveis
        const patterns = [
            // API Keys genéricas
            /[Aa]pi[_-]?[Kk]ey[\s=:]+[^\s\n]+/g,
            /[Aa]ccess[_-]?[Tt]oken[\s=:]+[^\s\n]+/g,
            /[Bb]earer[\s]+[^\s\n]+/g,
            // Tokens Discord
            /[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}/g,
            // OpenAI API Keys
            /sk-[A-Za-z0-9]{48}/g,
            // Anthropic API Keys
            /sk-ant-[A-Za-z0-9-]{95}/g,
            // URLs com tokens
            /https?:\/\/[^\s]*[?&](?:token|key|secret)=[^\s&]*/g
        ];

        let sanitized = str;
        patterns.forEach(pattern => {
            sanitized = sanitized.replace(pattern, '[REDACTED]');
        });

        return sanitized;
    }

    /**
     * CORREÇÃO CRÍTICA 4.1: Sanitizar objetos removendo dados sensíveis
     */
    sanitizeObject(obj) {
        if (!obj || typeof obj !== 'object') return obj;

        const sensitive = [
            'authorization', 'auth', 'token', 'key', 'secret', 'password',
            'api_key', 'apikey', 'access_token', 'bearer', 'x-api-key'
        ];

        const sanitized = Array.isArray(obj) ? [] : {};

        for (const [key, value] of Object.entries(obj)) {
            const keyLower = key.toLowerCase();
            const isSensitive = sensitive.some(s => keyLower.includes(s));

            if (isSensitive) {
                sanitized[key] = '[REDACTED]';
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeObject(value);
            } else if (typeof value === 'string') {
                sanitized[key] = this.sanitizeString(value);
            } else {
                sanitized[key] = value;
            }
        }

        return sanitized;
    }

    /**
     * CORREÇÃO CRÍTICA 4.1: Sanitizar URLs removendo parâmetros sensíveis
     */
    sanitizeURL(url) {
        if (!url || typeof url !== 'string') return url;

        try {
            const urlObj = new URL(url);

            // Remover parâmetros sensíveis
            const sensitiveParams = ['token', 'key', 'secret', 'auth', 'api_key'];
            sensitiveParams.forEach(param => {
                if (urlObj.searchParams.has(param)) {
                    urlObj.searchParams.set(param, '[REDACTED]');
                }
            });

            return urlObj.toString();
        } catch (error) {
            // Se não for uma URL válida, sanitizar como string
            return this.sanitizeString(url);
        }
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Configurar rate limiting inteligente
     */
    setupIntelligentRateLimiting() {
        // Limpeza automática de rate limits a cada 5 minutos
        setInterval(() => {
            this.cleanupIntelligentRateLimits();
        }, 5 * 60 * 1000);

        console.log('🔄 [IA] Sistema de rate limiting inteligente configurado');
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Verificar rate limit global
     */
    checkGlobalRateLimit(now) {
        const oneMinuteAgo = now - 60 * 1000;
        const oneHourAgo = now - 60 * 60 * 1000;

        // Filtrar requisições da última hora
        this.rateLimiter.global.requests = this.rateLimiter.global.requests.filter(
            timestamp => timestamp > oneHourAgo
        );

        // Verificar limite por hora
        if (this.rateLimiter.global.requests.length >= this.rateLimiter.global.maxPerHour) {
            return false;
        }

        // Verificar limite por minuto
        const requestsLastMinute = this.rateLimiter.global.requests.filter(
            timestamp => timestamp > oneMinuteAgo
        ).length;

        return requestsLastMinute < this.rateLimiter.global.maxPerMinute;
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Verificar rate limit por servidor
     */
    checkGuildRateLimit(guildId, now) {
        if (!this.rateLimiter.perGuild.has(guildId)) {
            this.initializeGuildRateLimit(guildId);
        }

        const guildLimiter = this.rateLimiter.perGuild.get(guildId);
        const oneMinuteAgo = now - 60 * 1000;

        // Filtrar requisições do último minuto
        guildLimiter.requests = guildLimiter.requests.filter(
            timestamp => timestamp > oneMinuteAgo
        );

        return guildLimiter.requests.length < guildLimiter.quota;
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Inicializar rate limit para servidor
     */
    initializeGuildRateLimit(guildId) {
        const guild = this.client.guilds.cache.get(guildId);
        let quota = this.rateLimiter.quotaLimits.medium; // Padrão

        if (guild) {
            const memberCount = guild.memberCount;

            if (memberCount < 100) {
                quota = this.rateLimiter.quotaLimits.small;
            } else if (memberCount > 1000) {
                quota = this.rateLimiter.quotaLimits.large;
            }

            // Verificar se é servidor premium (implementação futura)
            // if (this.isPremiumGuild(guildId)) {
            //     quota = this.rateLimiter.quotaLimits.premium;
            // }
        }

        this.rateLimiter.perGuild.set(guildId, {
            requests: [],
            quota: quota,
            memberCount: guild?.memberCount || 0
        });

        console.log(`🎯 [IA] Rate limit configurado para ${guildId}: ${quota} req/min (${guild?.memberCount || 0} membros)`);
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Limpeza inteligente de rate limits
     */
    cleanupIntelligentRateLimits() {
        const now = Date.now();
        const oneHourAgo = now - 60 * 60 * 1000;
        const oneMinuteAgo = now - 60 * 1000;

        // Limpar rate limit global
        this.rateLimiter.global.requests = this.rateLimiter.global.requests.filter(
            timestamp => timestamp > oneHourAgo
        );

        // Limpar rate limits por servidor
        for (const [guildId, limiter] of this.rateLimiter.perGuild.entries()) {
            limiter.requests = limiter.requests.filter(
                timestamp => timestamp > oneMinuteAgo
            );

            // Remover servidores inativos
            if (limiter.requests.length === 0 &&
                !this.client.guilds.cache.has(guildId)) {
                this.rateLimiter.perGuild.delete(guildId);
            }
        }

        // Estatísticas de limpeza
        const activeGuilds = this.rateLimiter.perGuild.size;
        const globalRequests = this.rateLimiter.global.requests.length;

        if (activeGuilds > 0 || globalRequests > 0) {
            console.log(`🧹 [IA] Rate limits limpos: ${activeGuilds} servidores ativos, ${globalRequests} requisições globais`);
        }
    }

    /**
     * CORREÇÃO CRÍTICA 4.2: Obter estatísticas de rate limiting
     */
    getRateLimitStats() {
        const now = Date.now();
        const oneMinuteAgo = now - 60 * 1000;
        const oneHourAgo = now - 60 * 60 * 1000;

        const globalStats = {
            requestsLastMinute: this.rateLimiter.global.requests.filter(t => t > oneMinuteAgo).length,
            requestsLastHour: this.rateLimiter.global.requests.filter(t => t > oneHourAgo).length,
            maxPerMinute: this.rateLimiter.global.maxPerMinute,
            maxPerHour: this.rateLimiter.global.maxPerHour
        };

        const guildStats = {};
        for (const [guildId, limiter] of this.rateLimiter.perGuild.entries()) {
            const requestsLastMinute = limiter.requests.filter(t => t > oneMinuteAgo).length;
            guildStats[guildId] = {
                requestsLastMinute,
                quota: limiter.quota,
                memberCount: limiter.memberCount,
                utilizationPercent: Math.round((requestsLastMinute / limiter.quota) * 100)
            };
        }

        return {
            global: globalStats,
            guilds: guildStats,
            totalActiveGuilds: this.rateLimiter.perGuild.size
        };
    }
}

module.exports = AIModeration;
