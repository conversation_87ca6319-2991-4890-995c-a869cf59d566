/**
 * ========================================
 * CORREÇÃO CRÍTICA 7.6: UTILITÁRIOS DE SEGURANÇA
 * Proteção contra ataques de timing e outras vulnerabilidades
 * ========================================
 */

const crypto = require('crypto');

class SecurityUtils {
    /**
     * Comparação segura de strings para prevenir timing attacks
     */
    static safeStringCompare(a, b) {
        if (typeof a !== 'string' || typeof b !== 'string') {
            return false;
        }

        if (a.length !== b.length) {
            // Ainda fazer comparação para evitar timing attack
            crypto.timingSafeEqual(Buffer.from(a), Buffer.from(a));
            return false;
        }

        try {
            return crypto.timingSafeEqual(Buffer.from(a), Buffer.from(b));
        } catch (error) {
            return false;
        }
    }

    /**
     * Gerar token seguro
     */
    static generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * Hash seguro com salt
     */
    static async secureHash(data, salt = null) {
        if (!salt) {
            salt = crypto.randomBytes(16).toString('hex');
        }

        return new Promise((resolve, reject) => {
            crypto.pbkdf2(data, salt, 100000, 64, 'sha512', (err, derivedKey) => {
                if (err) reject(err);
                resolve({
                    hash: derivedKey.toString('hex'),
                    salt: salt
                });
            });
        });
    }

    /**
     * Verificar hash com proteção contra timing attacks
     */
    static async verifyHash(data, hash, salt) {
        try {
            const computed = await this.secureHash(data, salt);
            return this.safeStringCompare(computed.hash, hash);
        } catch (error) {
            // Simular tempo de processamento mesmo em erro
            await new Promise(resolve => setTimeout(resolve, 100));
            return false;
        }
    }

    /**
     * Delay aleatório para prevenir timing attacks
     */
    static async randomDelay(minMs = 50, maxMs = 200) {
        const delay = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * Validar e sanitizar entrada com delay consistente
     */
    static async secureValidation(input, validator, options = {}) {
        const startTime = Date.now();
        const minProcessingTime = options.minTime || 100;

        try {
            const result = await validator(input);
            
            // Garantir tempo mínimo de processamento
            const elapsed = Date.now() - startTime;
            if (elapsed < minProcessingTime) {
                await new Promise(resolve => 
                    setTimeout(resolve, minProcessingTime - elapsed)
                );
            }

            return { valid: true, result };
        } catch (error) {
            // Garantir tempo mínimo mesmo em erro
            const elapsed = Date.now() - startTime;
            if (elapsed < minProcessingTime) {
                await new Promise(resolve => 
                    setTimeout(resolve, minProcessingTime - elapsed)
                );
            }

            return { valid: false, error: error.message };
        }
    }

    /**
     * Rate limiting com proteção contra timing attacks
     */
    static createSecureRateLimiter(maxAttempts = 5, windowMs = 15 * 60 * 1000) {
        const attempts = new Map();

        return async (identifier) => {
            const now = Date.now();
            const windowStart = now - windowMs;

            // Limpar tentativas antigas
            if (attempts.has(identifier)) {
                const userAttempts = attempts.get(identifier).filter(
                    timestamp => timestamp > windowStart
                );
                attempts.set(identifier, userAttempts);
            }

            const currentAttempts = attempts.get(identifier) || [];
            
            // Sempre adicionar delay para prevenir timing attacks
            await this.randomDelay(50, 150);

            if (currentAttempts.length >= maxAttempts) {
                return {
                    allowed: false,
                    remaining: 0,
                    resetTime: Math.min(...currentAttempts) + windowMs
                };
            }

            // Registrar tentativa
            currentAttempts.push(now);
            attempts.set(identifier, currentAttempts);

            return {
                allowed: true,
                remaining: maxAttempts - currentAttempts.length,
                resetTime: now + windowMs
            };
        };
    }

    /**
     * Mascarar dados sensíveis
     */
    static maskSensitiveData(data, type = 'default') {
        if (!data || typeof data !== 'string') return data;

        switch (type) {
            case 'email':
                const emailParts = data.split('@');
                if (emailParts.length === 2) {
                    const username = emailParts[0];
                    const domain = emailParts[1];
                    const maskedUsername = username.length > 2 
                        ? username[0] + '*'.repeat(username.length - 2) + username[username.length - 1]
                        : '*'.repeat(username.length);
                    return `${maskedUsername}@${domain}`;
                }
                break;

            case 'token':
                return data.length > 8 
                    ? data.substring(0, 4) + '*'.repeat(data.length - 8) + data.substring(data.length - 4)
                    : '*'.repeat(data.length);

            case 'ip':
                const ipParts = data.split('.');
                if (ipParts.length === 4) {
                    return `${ipParts[0]}.${ipParts[1]}.*.***`;
                }
                break;

            default:
                return data.length > 6 
                    ? data.substring(0, 3) + '*'.repeat(data.length - 6) + data.substring(data.length - 3)
                    : '*'.repeat(data.length);
        }

        return data;
    }

    /**
     * Verificar se uma string contém padrões maliciosos
     */
    static detectMaliciousPatterns(input) {
        if (!input || typeof input !== 'string') return false;

        const maliciousPatterns = [
            // SQL Injection
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
            /(--|\/\*|\*\/)/,
            /(\b(OR|AND)\b.*=.*)/i,
            /['"]\s*(OR|AND)\s*['"]/i,
            /;\s*(DROP|DELETE|INSERT|UPDATE)/i,

            // XSS
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/i,
            /on\w+\s*=/i,
            /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,

            // Command Injection
            /[;&|`$(){}[\]]/,
            /\b(eval|exec|system|shell_exec|passthru)\b/i,

            // Path Traversal
            /\.\.[\/\\]/,
            /[\/\\]\.\.[\/\\]/,

            // LDAP Injection
            /[()&|!]/,

            // NoSQL Injection
            /\$where/i,
            /\$ne/i,
            /\$gt/i,
            /\$lt/i
        ];

        return maliciousPatterns.some(pattern => pattern.test(input));
    }

    /**
     * Sanitizar entrada removendo padrões perigosos
     */
    static sanitizeInput(input, options = {}) {
        if (!input || typeof input !== 'string') return input;

        let sanitized = input;

        // Remover caracteres de controle
        sanitized = sanitized.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

        // Remover tags HTML se não permitidas
        if (!options.allowHtml) {
            sanitized = sanitized.replace(/<[^>]*>/g, '');
        }

        // Escapar caracteres especiais SQL
        if (!options.allowSqlChars) {
            sanitized = sanitized.replace(/['";\\]/g, '\\$&');
        }

        // Limitar tamanho
        if (options.maxLength && sanitized.length > options.maxLength) {
            sanitized = sanitized.substring(0, options.maxLength);
        }

        return sanitized;
    }

    /**
     * Gerar nonce para CSP
     */
    static generateCSPNonce() {
        return crypto.randomBytes(16).toString('base64');
    }

    /**
     * Verificar integridade de dados
     */
    static verifyIntegrity(data, expectedHash, algorithm = 'sha256') {
        try {
            const hash = crypto.createHash(algorithm).update(data).digest('hex');
            return this.safeStringCompare(hash, expectedHash);
        } catch (error) {
            return false;
        }
    }

    /**
     * Criar assinatura HMAC
     */
    static createHMACSignature(data, secret, algorithm = 'sha256') {
        try {
            return crypto.createHmac(algorithm, secret).update(data).digest('hex');
        } catch (error) {
            throw new Error('Erro ao criar assinatura HMAC');
        }
    }

    /**
     * Verificar assinatura HMAC
     */
    static verifyHMACSignature(data, signature, secret, algorithm = 'sha256') {
        try {
            const expectedSignature = this.createHMACSignature(data, secret, algorithm);
            return this.safeStringCompare(signature, expectedSignature);
        } catch (error) {
            return false;
        }
    }

    /**
     * Escape para uso em regex
     */
    static escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * Validar formato de email de forma segura
     */
    static isValidEmail(email) {
        if (!email || typeof email !== 'string') return false;
        
        // Regex simples e segura para email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) && email.length <= 254;
    }

    /**
     * Validar Discord ID
     */
    static isValidDiscordId(id) {
        if (!id || typeof id !== 'string') return false;
        return /^\d{17,19}$/.test(id);
    }
}

module.exports = SecurityUtils;
