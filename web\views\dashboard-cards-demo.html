<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Cards - Design Comercial Otimizado</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link rel="stylesheet" href="/css/dashboard-cards-premium.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-body">
        <main class="main-content">
            <div class="content-header">
                <div class="header-info">
                    <h1 class="page-title">
                        <i class="fas fa-cogs"></i>
                        Configurações do Servidor
                    </h1>
                    <p class="page-description">Configure os módulos e funcionalidades do bot para seu servidor</p>
                </div>
            </div>

            <!-- Grid de Configurações Otimizado -->
            <div class="config-grid">
                
                <!-- Card 1: Sistema de Logs (Exemplo Principal) -->
                <div class="config-card featured active">
                    <div class="config-header">
                        <div class="config-info">
                            <h3 class="config-title">
                                <i class="config-title-icon fas fa-file-alt"></i>
                                Sistema de Logs
                            </h3>
                            <p class="config-description">
                                Registra todas as atividades importantes do servidor
                            </p>
                            <div class="config-status enabled">
                                <span class="config-status-dot"></span>
                                Ativo
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-content">
                        <div class="toggle-group primary">
                            <label class="toggle-label primary">Ativar Sistema de Logs</label>
                            <label class="toggle">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="config-subsection">
                            <div class="subsection-title">Configurações Avançadas</div>
                            
                            <div class="toggle-group">
                                <label class="toggle-label">Logs de Mensagens</label>
                                <label class="toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="toggle-group">
                                <label class="toggle-label">Logs de Moderação</label>
                                <label class="toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="config-progress">
                            <div class="config-progress-bar" style="width: 85%"></div>
                        </div>
                        
                        <div class="config-quick-actions">
                            <button class="quick-action-btn">
                                <i class="fas fa-cog"></i> Configurar
                            </button>
                            <button class="quick-action-btn">
                                <i class="fas fa-eye"></i> Visualizar
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Card 2: Moderação IA -->
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-info">
                            <h3 class="config-title">
                                <i class="config-title-icon fas fa-brain"></i>
                                Moderação com IA
                            </h3>
                            <p class="config-description">
                                Sistema inteligente que detecta conteúdo inadequado automaticamente
                            </p>
                            <div class="config-status disabled">
                                <span class="config-status-dot"></span>
                                Inativo
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-content">
                        <div class="toggle-group primary">
                            <label class="toggle-label primary">Ativar IA de Moderação</label>
                            <label class="toggle">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="config-subsection disabled">
                            <div class="subsection-title">Configurações de Sensibilidade</div>
                            
                            <div class="toggle-group">
                                <label class="toggle-label">Detecção de Toxicidade</label>
                                <label class="toggle">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="config-dependency info">
                            Requer configuração de canal de logs para funcionar
                        </div>
                    </div>
                </div>

                <!-- Card 3: Sistema Anti-Raid -->
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-info">
                            <h3 class="config-title">
                                <i class="config-title-icon fas fa-shield-alt"></i>
                                Sistema Anti-Raid
                            </h3>
                            <p class="config-description">
                                Proteção automática contra ataques de raid e spam
                            </p>
                            <div class="config-status enabled">
                                <span class="config-status-dot"></span>
                                Ativo
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-content">
                        <div class="toggle-group primary">
                            <label class="toggle-label primary">Ativar Anti-Raid</label>
                            <label class="toggle">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="config-subsection">
                            <div class="subsection-title">Configurações de Proteção</div>
                            
                            <div class="toggle-group">
                                <label class="toggle-label">Quarentena Automática</label>
                                <label class="toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="toggle-group">
                                <label class="toggle-label">Verificação de Idade da Conta</label>
                                <label class="toggle">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="config-progress">
                            <div class="config-progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <!-- Card 4: Sistema de Verificação -->
                <div class="config-card warning">
                    <div class="config-header">
                        <div class="config-info">
                            <h3 class="config-title">
                                <i class="config-title-icon fas fa-user-check"></i>
                                Sistema de Verificação
                            </h3>
                            <p class="config-description">
                                Verificação de novos membros com CAPTCHA e validações
                            </p>
                            <div class="config-status disabled">
                                <span class="config-status-dot"></span>
                                Configuração Incompleta
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-content">
                        <div class="toggle-group primary">
                            <label class="toggle-label primary">Ativar Verificação</label>
                            <label class="toggle">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="config-dependency">
                            Configure um canal de verificação antes de ativar este módulo
                        </div>
                        
                        <div class="config-quick-actions">
                            <button class="quick-action-btn">
                                <i class="fas fa-wrench"></i> Configurar Canal
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Card 5: Analytics -->
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-info">
                            <h3 class="config-title">
                                <i class="config-title-icon fas fa-chart-bar"></i>
                                Analytics
                            </h3>
                            <p class="config-description">
                                Estatísticas detalhadas sobre atividade do servidor
                            </p>
                            <div class="config-status enabled">
                                <span class="config-status-dot"></span>
                                Coletando Dados
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-content">
                        <div class="toggle-group primary">
                            <label class="toggle-label primary">Ativar Analytics</label>
                            <label class="toggle">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="config-progress">
                            <div class="config-progress-bar" style="width: 65%"></div>
                        </div>
                    </div>
                </div>

                <!-- Card 6: Sistema de Tickets -->
                <div class="config-card">
                    <div class="config-header">
                        <div class="config-info">
                            <h3 class="config-title">
                                <i class="config-title-icon fas fa-ticket-alt"></i>
                                Sistema de Tickets
                            </h3>
                            <p class="config-description">
                                Suporte organizado através de tickets privados
                            </p>
                            <div class="config-status disabled">
                                <span class="config-status-dot"></span>
                                Inativo
                            </div>
                        </div>
                    </div>
                    
                    <div class="config-content">
                        <div class="toggle-group primary">
                            <label class="toggle-label primary">Ativar Sistema de Tickets</label>
                            <label class="toggle">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="config-dependency info">
                            Funcionalidade premium - Configure categorias e permissões
                        </div>
                    </div>
                </div>

            </div>
        </main>
    </div>

    <script>
        // Simular interações dos toggles
        document.querySelectorAll('.toggle input').forEach(toggle => {
            toggle.addEventListener('change', function() {
                const card = this.closest('.config-card');
                const subsection = card.querySelector('.config-subsection');
                const status = card.querySelector('.config-status');
                
                if (this.checked) {
                    card.classList.add('active');
                    if (subsection) subsection.classList.remove('disabled');
                    if (status) {
                        status.classList.remove('disabled');
                        status.classList.add('enabled');
                        status.innerHTML = '<span class="config-status-dot"></span>Ativo';
                    }
                } else {
                    card.classList.remove('active');
                    if (subsection) subsection.classList.add('disabled');
                    if (status) {
                        status.classList.remove('enabled');
                        status.classList.add('disabled');
                        status.innerHTML = '<span class="config-status-dot"></span>Inativo';
                    }
                }
            });
        });
    </script>
</body>
</html>
