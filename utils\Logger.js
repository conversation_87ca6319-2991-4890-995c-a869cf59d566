/**
 * ========================================
 * SISTEMA DE LOGS SIMPLIFICADO
 * Logger básico para o bot
 * ========================================
 */

const fs = require('fs');
const path = require('path');
const moment = require('moment-timezone');

class Logger {
    constructor() {
        this.timezone = process.env.TIMEZONE || 'America/Sao_Paulo';
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.logDir = './logs';

        // CORREÇÃO CRÍTICA 2.2: Configurações de rotação de logs
        this.maxFileSize = 10 * 1024 * 1024; // 10MB por arquivo
        this.maxFiles = 10; // Manter até 10 arquivos de log
        this.currentLogFile = null;
        this.currentFileSize = 0;

        // Criar diretório de logs se não existir
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }

        // Iniciar rotação automática
        this.startLogRotation();
    }

    /**
     * Formata timestamp
     */
    getTimestamp() {
        return moment().tz(this.timezone).format('DD/MM/YYYY HH:mm:ss');
    }

    /**
     * Escreve log no arquivo
     */
    writeToFile(level, message, meta = {}) {
        try {
            const timestamp = this.getTimestamp();

            // CORREÇÃO CRÍTICA 2.1: Sanitizar dados sensíveis
            const sanitizedMessage = this.sanitizeMessage(message);
            const sanitizedMeta = this.sanitizeLogData(meta);

            const logEntry = `[${timestamp}] ${level.toUpperCase()}: ${sanitizedMessage}`;
            const metaStr = Object.keys(sanitizedMeta).length > 0 ? ` | Meta: ${JSON.stringify(sanitizedMeta)}` : '';
            const fullLog = logEntry + metaStr + '\n';

            // CORREÇÃO CRÍTICA 2.2: Usar rotação de logs
            const logFile = this.getLogFile();

            // Verificar se precisa rotacionar antes de escrever
            this.checkAndRotateLog(fullLog.length);

            fs.appendFileSync(logFile, fullLog);
            this.currentFileSize += fullLog.length;

        } catch (error) {
            console.error('Erro ao escrever log:', error);
        }
    }

    /**
     * Log de informação
     */
    info(message, meta = {}) {
        console.log(`ℹ️  ${message}`);
        this.writeToFile('info', message, meta);
    }

    /**
     * Log de erro
     */
    error(message, error = null, meta = {}) {
        console.error(`❌ ${message}`, error || '');
        
        const errorMeta = { ...meta };
        if (error instanceof Error) {
            errorMeta.error = error.message;
            errorMeta.stack = error.stack;
        } else if (error) {
            errorMeta.error = error;
        }
        
        this.writeToFile('error', message, errorMeta);
    }

    /**
     * Log de aviso
     */
    warn(message, meta = {}) {
        console.warn(`⚠️  ${message}`);
        this.writeToFile('warn', message, meta);
    }

    /**
     * Log de debug
     */
    debug(message, meta = {}) {
        if (this.logLevel === 'debug') {
            console.log(`🔍 ${message}`);
            this.writeToFile('debug', message, meta);
        }
    }

    /**
     * Log específico para moderação
     */
    moderation(action, guildId, userId, moderatorId, reason, meta = {}) {
        const message = `MODERAÇÃO: ${action}`;
        this.info(message, {
            guild: guildId,
            user: userId,
            moderator: moderatorId,
            reason: reason,
            ...meta
        });
    }

    /**
     * Log específico para anti-raid
     */
    antiRaid(event, guildId, severity, details = {}) {
        const message = `ANTI-RAID: ${event}`;
        this.warn(message, {
            guild: guildId,
            severity: severity,
            ...details
        });
    }

    /**
     * Log específico para comandos
     */
    command(commandName, userId, guildId, success = true, error = null) {
        const level = success ? 'info' : 'warn';
        const message = `COMANDO: ${commandName} ${success ? 'executado' : 'falhou'}`;
        
        this[level](message, {
            command: commandName,
            user: userId,
            guild: guildId,
            success: success,
            error: error
        });
    }

    /**
     * Log de performance
     */
    performance(operation, duration, meta = {}) {
        const level = duration > 1000 ? 'warn' : 'debug';
        const message = `PERFORMANCE: ${operation} levou ${duration}ms`;
        this[level](message, {
            operation,
            duration,
            ...meta
        });
    }

    /**
     * Log de inicialização do sistema
     */
    startup(component, status, details = {}) {
        const level = status === 'success' ? 'info' : 'error';
        const message = `STARTUP: ${component} - ${status}`;
        
        this[level](message, {
            component,
            status,
            ...details
        });
    }

    /**
     * Limpa logs antigos
     */
    cleanup(daysToKeep = 30) {
        try {
            const cutoffDate = moment().subtract(daysToKeep, 'days');
            const logFiles = fs.readdirSync(this.logDir);
            let deletedCount = 0;

            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const fileStat = fs.statSync(filePath);

                if (moment(fileStat.mtime).isBefore(cutoffDate)) {
                    try {
                        fs.unlinkSync(filePath);
                        deletedCount++;
                        this.debug(`Log antigo removido: ${file}`);
                    } catch (error) {
                        this.error(`Erro ao remover log antigo ${file}:`, error);
                    }
                }
            }

            this.info(`Limpeza de logs concluída: ${deletedCount} arquivos removidos`);
            return deletedCount;
        } catch (error) {
            this.error('Erro na limpeza de logs:', error);
            return 0;
        }
    }

    /**
     * CORREÇÃO CRÍTICA 2.1: Sanitizar dados sensíveis em logs
     */
    sanitizeLogData(data) {
        if (!data || typeof data !== 'object') return data;

        const sensitive = [
            'password', 'token', 'key', 'secret', 'auth', 'authorization',
            'ip', 'ip_address', 'ipAddress', 'email', 'phone', 'cpf', 'cnpj',
            'credit_card', 'creditCard', 'card_number', 'cardNumber',
            'api_key', 'apiKey', 'access_token', 'accessToken', 'refresh_token',
            'discord_token', 'bot_token', 'webhook_url', 'database_url'
        ];

        const sanitized = Array.isArray(data) ? [] : {};

        for (const [key, value] of Object.entries(data)) {
            const keyLower = key.toLowerCase();
            const isSensitive = sensitive.some(s => keyLower.includes(s));

            if (isSensitive) {
                sanitized[key] = '[REDACTED]';
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = this.sanitizeLogData(value);
            } else {
                sanitized[key] = value;
            }
        }

        return sanitized;
    }

    /**
     * CORREÇÃO CRÍTICA 2.1: Sanitizar mensagens de log
     */
    sanitizeMessage(message) {
        if (!message || typeof message !== 'string') return message;

        // Padrões para detectar dados sensíveis
        const patterns = [
            // Tokens Discord
            /[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}/g,
            // IPs
            /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
            // Emails
            /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            // URLs com tokens
            /https?:\/\/[^\s]*[?&](?:token|key|secret)=[^\s&]*/g,
            // Chaves API genéricas
            /(?:api[_-]?key|access[_-]?token|secret[_-]?key)[\s=:]+[^\s\n]+/gi
        ];

        let sanitized = message;
        patterns.forEach(pattern => {
            sanitized = sanitized.replace(pattern, '[REDACTED]');
        });

        return sanitized;
    }

    /**
     * CORREÇÃO CRÍTICA 2.2: Obter arquivo de log atual
     */
    getLogFile() {
        if (!this.currentLogFile) {
            const today = moment().tz(this.timezone).format('YYYY-MM-DD');
            this.currentLogFile = path.join(this.logDir, `nova-moderacao-${today}.log`);

            // Verificar tamanho do arquivo atual
            if (fs.existsSync(this.currentLogFile)) {
                const stats = fs.statSync(this.currentLogFile);
                this.currentFileSize = stats.size;
            } else {
                this.currentFileSize = 0;
            }
        }
        return this.currentLogFile;
    }

    /**
     * CORREÇÃO CRÍTICA 2.2: Verificar e rotacionar log se necessário
     */
    checkAndRotateLog(newDataSize = 0) {
        const logFile = this.getLogFile();

        // Verificar se o arquivo atual excederá o tamanho máximo
        if (this.currentFileSize + newDataSize > this.maxFileSize) {
            this.rotateLog();
        }

        // Verificar se mudou o dia (rotação diária)
        const today = moment().tz(this.timezone).format('YYYY-MM-DD');
        const currentDate = path.basename(this.currentLogFile).match(/nova-moderacao-(\d{4}-\d{2}-\d{2})/);

        if (!currentDate || currentDate[1] !== today) {
            this.rotateLog();
        }
    }

    /**
     * CORREÇÃO CRÍTICA 2.2: Rotacionar arquivo de log
     */
    rotateLog() {
        try {
            if (this.currentLogFile && fs.existsSync(this.currentLogFile)) {
                // Criar nome do arquivo rotacionado
                const timestamp = moment().tz(this.timezone).format('YYYY-MM-DD_HH-mm-ss');
                const baseName = path.basename(this.currentLogFile, '.log');
                const rotatedName = `${baseName}_${timestamp}.log`;
                const rotatedPath = path.join(this.logDir, rotatedName);

                // Mover arquivo atual para arquivo rotacionado
                fs.renameSync(this.currentLogFile, rotatedPath);

                console.log(`📁 Log rotacionado: ${rotatedName}`);
            }

            // Resetar arquivo atual
            const today = moment().tz(this.timezone).format('YYYY-MM-DD');
            this.currentLogFile = path.join(this.logDir, `nova-moderacao-${today}.log`);
            this.currentFileSize = 0;

            // Limpar arquivos antigos
            this.cleanupRotatedLogs();

        } catch (error) {
            console.error('Erro ao rotacionar log:', error);
        }
    }

    /**
     * CORREÇÃO CRÍTICA 2.2: Limpar arquivos de log rotacionados antigos
     */
    cleanupRotatedLogs() {
        try {
            const logFiles = fs.readdirSync(this.logDir)
                .filter(file => file.startsWith('nova-moderacao-') && file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(this.logDir, file),
                    stats: fs.statSync(path.join(this.logDir, file))
                }))
                .sort((a, b) => b.stats.mtime - a.stats.mtime); // Mais recente primeiro

            // Manter apenas os arquivos mais recentes
            if (logFiles.length > this.maxFiles) {
                const filesToDelete = logFiles.slice(this.maxFiles);

                for (const file of filesToDelete) {
                    try {
                        fs.unlinkSync(file.path);
                        console.log(`🗑️ Log antigo removido: ${file.name}`);
                    } catch (error) {
                        console.error(`Erro ao remover log ${file.name}:`, error);
                    }
                }
            }

        } catch (error) {
            console.error('Erro na limpeza de logs rotacionados:', error);
        }
    }

    /**
     * CORREÇÃO CRÍTICA 2.2: Iniciar rotação automática de logs
     */
    startLogRotation() {
        // Verificar rotação a cada hora
        setInterval(() => {
            this.checkAndRotateLog();
        }, 60 * 60 * 1000); // 1 hora

        // Limpeza diária de logs antigos
        setInterval(() => {
            this.cleanup(30); // Manter logs por 30 dias
        }, 24 * 60 * 60 * 1000); // 24 horas

        console.log('🔄 Sistema de rotação de logs iniciado');
    }
}

module.exports = Logger;
