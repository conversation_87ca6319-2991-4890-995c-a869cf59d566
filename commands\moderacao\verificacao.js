/**
 * ========================================
 * SISTEMA DE VERIFICAÇÃO PREMIUM
 * Sistema completo de verificação de membros
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle, ChannelType } = require('discord.js');
const EmbedStyles = require('../../utils/EmbedStyles');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('verificacao')
        .setDescription('🛡️ Sistema completo de verificação de membros')
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Configurar sistema de verificação')
                .addChannelOption(option =>
                    option.setName('canal')
                        .setDescription('Canal para verificação')
                        .addChannelTypes(ChannelType.GuildText)
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('metodo')
                        .setDescription('Método de verificação')
                        .setRequired(true)
                        .addChoices(
                            { name: '🎯 Reação em Embed', value: 'reaction' },
                            { name: '🧮 Captcha Matemático', value: 'captcha' },
                            { name: '👥 Verificação Manual', value: 'manual' },
                            { name: '🔗 Combinado (Reação + Captcha)', value: 'combined' }
                        ))
                .addRoleOption(option =>
                    option.setName('cargo_verificado')
                        .setDescription('Cargo para membros verificados')
                        .setRequired(true))
                .addRoleOption(option =>
                    option.setName('cargo_nao_verificado')
                        .setDescription('Cargo para membros não verificados')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Configurar opções avançadas')
                .addIntegerOption(option =>
                    option.setName('tempo_limite')
                        .setDescription('Tempo limite em minutos (padrão: 30)')
                        .setMinValue(5)
                        .setMaxValue(1440)
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('tipo_captcha')
                        .setDescription('Tipo de captcha')
                        .addChoices(
                            { name: '🧮 Matemático', value: 'math' },
                            { name: '📝 Texto', value: 'text' }
                        )
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('regras')
                        .setDescription('Texto das regras (máximo 1000 caracteres)')
                        .setMaxLength(1000)
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('mensagem_boas_vindas')
                        .setDescription('Mensagem de boas-vindas após verificação')
                        .setMaxLength(500)
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Ver status do sistema de verificação'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('Estatísticas de verificação'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Resetar configurações de verificação'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('verificar')
                .setDescription('Verificar usuário manualmente')
                .addUserOption(option =>
                    option.setName('usuario')
                        .setDescription('Usuário para verificar')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('desverificar')
                .setDescription('Remover verificação de usuário')
                .addUserOption(option =>
                    option.setName('usuario')
                        .setDescription('Usuário para desverificar')
                        .setRequired(true)))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false),

    async execute(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'setup':
                    await this.setupVerification(interaction);
                    break;
                case 'config':
                    await this.configureVerification(interaction);
                    break;
                case 'status':
                    await this.showStatus(interaction);
                    break;
                case 'stats':
                    await this.showStats(interaction);
                    break;
                case 'reset':
                    await this.resetVerification(interaction);
                    break;
                case 'verificar':
                    await this.manualVerify(interaction);
                    break;
                case 'desverificar':
                    await this.manualUnverify(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: 'Subcomando não reconhecido!',
                        ephemeral: true
                    });
            }
        } catch (error) {
            client.logger.error('Erro no comando verificacao:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao processar o comando de verificação.'
            );
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    /**
     * Configurar sistema de verificação
     */
    async setupVerification(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const channel = interaction.options.getChannel('canal');
        const method = interaction.options.getString('metodo');
        const verifiedRole = interaction.options.getRole('cargo_verificado');
        const unverifiedRole = interaction.options.getRole('cargo_nao_verificado');

        // Verificações de segurança
        if (!guild.members.me.permissions.has([PermissionFlagsBits.ManageRoles, PermissionFlagsBits.SendMessages])) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Permissões Insuficientes',
                'Preciso das permissões de **Gerenciar Cargos** e **Enviar Mensagens** para configurar o sistema!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        if (verifiedRole.position >= guild.members.me.roles.highest.position) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Hierarquia Insuficiente',
                'O cargo de verificado deve estar abaixo do meu cargo mais alto!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        if (unverifiedRole && unverifiedRole.position >= guild.members.me.roles.highest.position) {
            const errorEmbed = embedStyles.createErrorEmbed(
                'Hierarquia Insuficiente',
                'O cargo de não verificado deve estar abaixo do meu cargo mais alto!'
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        await interaction.deferReply();

        try {
            // Criar embed de verificação
            const verificationEmbed = await this.createVerificationEmbed(method, guild);
            
            // Enviar mensagem no canal
            const sentMessage = await channel.send({ 
                embeds: [verificationEmbed.embed], 
                components: verificationEmbed.components 
            });

            // Salvar configuração no banco
            const config = {
                guild_id: guild.id,
                enabled: true,
                channel_id: channel.id,
                method: method,
                message_id: sentMessage.id,
                verified_role_id: verifiedRole.id,
                unverified_role_id: unverifiedRole?.id || null,
                captcha_type: 'math',
                timeout_minutes: 30,
                rules_text: 'Leia e aceite as regras do servidor para continuar.',
                welcome_message: 'Bem-vindo(a) ao servidor! Você foi verificado(a) com sucesso.',
                settings: JSON.stringify({})
            };

            const stmt = client.database.db.prepare(`
                INSERT OR REPLACE INTO verification_config 
                (guild_id, enabled, channel_id, method, message_id, verified_role_id, unverified_role_id, 
                 captcha_type, timeout_minutes, rules_text, welcome_message, settings, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);

            stmt.run(
                config.guild_id, config.enabled, config.channel_id, config.method,
                config.message_id, config.verified_role_id, config.unverified_role_id,
                config.captcha_type, config.timeout_minutes, config.rules_text,
                config.welcome_message, config.settings
            );

            // Embed de sucesso
            const successEmbed = {
                color: parseInt(embedStyles.colors.success.replace('#', ''), 16),
                title: `${embedStyles.icons.success} ${embedStyles.format.bold('Sistema de Verificação Configurado!')}`,
                description: `**O sistema de verificação foi configurado com sucesso**\n\n${embedStyles.format.italic('Novos membros agora passarão pelo processo de verificação')}`,
                fields: [
                    {
                        name: `${embedStyles.icons.channel} **Canal de Verificação**`,
                        value: `${channel}\n${embedStyles.format.code(channel.id)}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.settings} **Método Configurado**`,
                        value: this.getMethodDisplay(method),
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.role} **Cargos Configurados**`,
                        value: `**Verificado:** ${verifiedRole}\n**Não Verificado:** ${unverifiedRole || 'Nenhum'}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info} **Próximos Passos**`,
                        value: `• Configure opções avançadas com \`/verificacao config\`\n• Monitore estatísticas com \`/verificacao stats\`\n• Acesse o dashboard para configurações detalhadas`,
                        inline: false
                    }
                ],
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Sistema de Verificação Premium',
                    icon_url: guild.iconURL()
                }
            };

            await interaction.editReply({ embeds: [successEmbed] });

            // Log da ação
            client.logger.info(`Sistema de verificação configurado por ${interaction.user.tag} no servidor ${guild.name}`);

        } catch (error) {
            client.logger.error('Erro ao configurar verificação:', error);
            
            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro na Configuração',
                'Ocorreu um erro ao configurar o sistema de verificação.'
            );
            
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Criar embed de verificação
     */
    async createVerificationEmbed(method, guild) {
        const embedStyles = new EmbedStyles();
        
        const embed = {
            color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
            title: `${embedStyles.icons.shield} ${embedStyles.format.bold('Verificação de Membro')}`,
            description: `**Bem-vindo(a) ao ${embedStyles.format.bold(guild.name)}!**\n\n${embedStyles.format.italic('Para acessar o servidor, você precisa se verificar primeiro.')}`,
            fields: [
                {
                    name: `${embedStyles.icons.info} **Como Funciona**`,
                    value: this.getMethodInstructions(method),
                    inline: false
                },
                {
                    name: `${embedStyles.icons.rules} **Regras Importantes**`,
                    value: `• Leia e aceite as regras do servidor\n• Seja respeitoso com todos os membros\n• Não faça spam ou conteúdo inadequado\n• Siga as diretrizes da comunidade Discord`,
                    inline: false
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Nodex | Moderação • Sistema de Verificação Premium',
                icon_url: guild.iconURL()
            },
            thumbnail: {
                url: guild.iconURL({ dynamic: true })
            }
        };

        let components = [];

        if (method === 'reaction' || method === 'combined') {
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('verify_start')
                        .setLabel('Iniciar Verificação')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅')
                );
            components.push(row);
        }

        return { embed, components };
    },

    /**
     * Obter instruções do método
     */
    getMethodInstructions(method) {
        const instructions = {
            'reaction': '1️⃣ Clique no botão **"Iniciar Verificação"** abaixo\n2️⃣ Confirme que leu e aceita as regras\n3️⃣ Receba seu cargo de membro verificado!',
            'captcha': '1️⃣ Use o comando `/verificar` para iniciar\n2️⃣ Resolva o captcha matemático apresentado\n3️⃣ Receba seu cargo de membro verificado!',
            'manual': '1️⃣ Aguarde a verificação manual da equipe\n2️⃣ Um moderador irá revisar seu perfil\n3️⃣ Você será notificado quando aprovado!',
            'combined': '1️⃣ Clique no botão **"Iniciar Verificação"** abaixo\n2️⃣ Resolva o captcha matemático\n3️⃣ Confirme que aceita as regras\n4️⃣ Receba seu cargo de membro verificado!'
        };
        return instructions[method] || 'Método de verificação não reconhecido.';
    },

    /**
     * Configurar opções avançadas
     */
    async configureVerification(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;

        await interaction.deferReply();

        try {
            // Buscar configuração atual
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            if (!config) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Sistema Não Configurado',
                    'Configure o sistema primeiro com `/verificacao setup`!'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Obter opções
            const timeoutMinutes = interaction.options.getInteger('tempo_limite') || config.timeout_minutes;
            const captchaType = interaction.options.getString('tipo_captcha') || config.captcha_type;
            const rulesText = interaction.options.getString('regras') || config.rules_text;
            const welcomeMessage = interaction.options.getString('mensagem_boas_vindas') || config.welcome_message;

            // Atualizar configuração
            const updateStmt = client.database.db.prepare(`
                UPDATE verification_config
                SET timeout_minutes = ?, captcha_type = ?, rules_text = ?, welcome_message = ?, updated_at = CURRENT_TIMESTAMP
                WHERE guild_id = ?
            `);

            updateStmt.run(timeoutMinutes, captchaType, rulesText, welcomeMessage, guild.id);

            const successEmbed = embedStyles.createSuccessEmbed(
                'Configurações Atualizadas',
                `As configurações avançadas foram atualizadas com sucesso!\n\n**Tempo Limite:** ${timeoutMinutes} minutos\n**Tipo de Captcha:** ${captchaType === 'math' ? '🧮 Matemático' : '📝 Texto'}\n**Regras:** ${rulesText.length > 50 ? rulesText.substring(0, 50) + '...' : rulesText}`
            );

            await interaction.editReply({ embeds: [successEmbed] });

        } catch (error) {
            client.logger.error('Erro ao configurar verificação:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro na Configuração',
                'Ocorreu um erro ao atualizar as configurações.'
            );

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Mostrar status do sistema
     */
    async showStatus(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;

        await interaction.deferReply();

        try {
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            if (!config) {
                const errorEmbed = embedStyles.createWarningEmbed(
                    'Sistema Não Configurado',
                    'O sistema de verificação ainda não foi configurado neste servidor.\n\nUse `/verificacao setup` para começar!'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            const channel = guild.channels.cache.get(config.channel_id);
            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            const unverifiedRole = config.unverified_role_id ? guild.roles.cache.get(config.unverified_role_id) : null;

            const statusEmbed = {
                color: config.enabled ?
                    parseInt(embedStyles.colors.success.replace('#', ''), 16) :
                    parseInt(embedStyles.colors.warning.replace('#', ''), 16),
                title: `${config.enabled ? embedStyles.icons.success : embedStyles.icons.warning} ${embedStyles.format.bold('Status do Sistema de Verificação')}`,
                description: `**Sistema está ${config.enabled ? 'ATIVO' : 'INATIVO'}**\n\n${embedStyles.format.italic('Configuração atual do sistema de verificação')}`,
                fields: [
                    {
                        name: `${embedStyles.icons.settings} **Configuração Principal**`,
                        value: `**Status:** ${config.enabled ? '🟢 Ativo' : '🔴 Inativo'}\n**Método:** ${this.getMethodDisplay(config.method)}\n**Canal:** ${channel || '❌ Canal removido'}\n**Método DB:** \`${config.method}\``,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.role} **Cargos Configurados**`,
                        value: `**Verificado:** ${verifiedRole || '❌ Cargo removido'}\n**Não Verificado:** ${unverifiedRole || 'Nenhum'}\n**Hierarquia:** ${verifiedRole && verifiedRole.position < guild.members.me.roles.highest.position ? '✅ OK' : '❌ Problema'}`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info} **Configurações Avançadas**`,
                        value: `**Tempo Limite:** ${config.timeout_minutes} minutos\n**Tipo de Captcha:** ${config.captcha_type === 'math' ? '🧮 Matemático' : '📝 Texto'}\n**Última Atualização:** ${embedStyles.format.timestampRelative(new Date(config.updated_at))}`,
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Status do Sistema',
                    icon_url: guild.iconURL()
                }
            };

            // Adicionar campo com regras se existir
            if (config.rules_text && config.rules_text.length > 0) {
                statusEmbed.fields.push({
                    name: `${embedStyles.icons.rules} **Regras Configuradas**`,
                    value: config.rules_text.length > 100 ?
                        config.rules_text.substring(0, 100) + '...' :
                        config.rules_text,
                    inline: false
                });
            }

            // Adicionar campo de debug
            statusEmbed.fields.push({
                name: `🔧 **Debug Info**`,
                value: `**Message ID:** \`${config.message_id || 'N/A'}\`\n**Verification Method:** \`${config.verification_method || config.method || 'N/A'}\`\n**Captcha Type:** \`${config.verification_captcha_type || config.captcha_type || 'N/A'}\``,
                inline: false
            });

            await interaction.editReply({ embeds: [statusEmbed] });

        } catch (error) {
            client.logger.error('Erro ao mostrar status:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao buscar o status do sistema.'
            );

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Mostrar estatísticas
     */
    async showStats(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;

        await interaction.deferReply();

        try {
            // Buscar estatísticas
            const totalVerified = client.database.db.prepare(`
                SELECT COUNT(*) as count FROM member_verification
                WHERE guild_id = ? AND verified = 1
            `).get(guild.id).count;

            const totalPending = client.database.db.prepare(`
                SELECT COUNT(*) as count FROM member_verification
                WHERE guild_id = ? AND verified = 0
            `).get(guild.id).count;

            const recentVerifications = client.database.db.prepare(`
                SELECT COUNT(*) as count FROM verification_logs
                WHERE guild_id = ? AND action = 'completed' AND timestamp > datetime('now', '-24 hours')
            `).get(guild.id).count;

            const failedAttempts = client.database.db.prepare(`
                SELECT COUNT(*) as count FROM verification_logs
                WHERE guild_id = ? AND action = 'failed' AND timestamp > datetime('now', '-7 days')
            `).get(guild.id).count;

            const statsEmbed = {
                color: parseInt(embedStyles.colors.info.replace('#', ''), 16),
                title: `${embedStyles.icons.analytics} ${embedStyles.format.bold('Estatísticas de Verificação')}`,
                description: `**Análise completa do sistema de verificação**\n\n${embedStyles.format.italic('Dados atualizados em tempo real')}`,
                fields: [
                    {
                        name: `${embedStyles.icons.success} **Membros Verificados**`,
                        value: `**Total:** ${totalVerified}\n**Últimas 24h:** ${recentVerifications}\n**Taxa de Sucesso:** ${totalVerified > 0 ? Math.round((totalVerified / (totalVerified + failedAttempts)) * 100) : 0}%`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.warning} **Pendentes/Falharam**`,
                        value: `**Pendentes:** ${totalPending}\n**Falharam (7 dias):** ${failedAttempts}\n**Taxa de Falha:** ${failedAttempts > 0 ? Math.round((failedAttempts / (totalVerified + failedAttempts)) * 100) : 0}%`,
                        inline: true
                    },
                    {
                        name: `${embedStyles.icons.info} **Performance**`,
                        value: `**Sistema:** 🟢 Funcionando\n**Média Diária:** ${Math.round(totalVerified / 30)}\n**Eficiência:** ${totalVerified > failedAttempts ? '🟢 Alta' : '🟡 Média'}`,
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Nodex | Moderação • Estatísticas Premium',
                    icon_url: guild.iconURL()
                }
            };

            await interaction.editReply({ embeds: [statsEmbed] });

        } catch (error) {
            client.logger.error('Erro ao buscar estatísticas:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao buscar as estatísticas.'
            );

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Resetar sistema
     */
    async resetVerification(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;

        await interaction.deferReply();

        try {
            // Remover configuração
            client.database.db.prepare(`DELETE FROM verification_config WHERE guild_id = ?`).run(guild.id);
            client.database.db.prepare(`DELETE FROM member_verification WHERE guild_id = ?`).run(guild.id);
            client.database.db.prepare(`DELETE FROM verification_logs WHERE guild_id = ?`).run(guild.id);

            const successEmbed = embedStyles.createSuccessEmbed(
                'Sistema Resetado',
                'O sistema de verificação foi resetado completamente!\n\n• Todas as configurações foram removidas\n• Histórico de verificações foi limpo\n• Logs foram apagados\n\nUse `/verificacao setup` para reconfigurar.'
            );

            await interaction.editReply({ embeds: [successEmbed] });

            client.logger.info(`Sistema de verificação resetado por ${interaction.user.tag} no servidor ${guild.name}`);

        } catch (error) {
            client.logger.error('Erro ao resetar sistema:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao resetar o sistema.'
            );

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Verificação manual
     */
    async manualVerify(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const user = interaction.options.getUser('usuario');

        await interaction.deferReply();

        try {
            const member = await guild.members.fetch(user.id).catch(() => null);
            if (!member) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Usuário Não Encontrado',
                    'Este usuário não está no servidor!'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Buscar configuração
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            if (!config) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Sistema Não Configurado',
                    'Configure o sistema primeiro com `/verificacao setup`!'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Verificar usuário
            await this.verifyMember(member, config, 'manual', interaction.user.id);

            const successEmbed = embedStyles.createSuccessEmbed(
                'Usuário Verificado',
                `${user.tag} foi verificado manualmente com sucesso!\n\n• Cargo de verificado atribuído\n• Status atualizado no sistema\n• Log registrado`
            );

            await interaction.editReply({ embeds: [successEmbed] });

        } catch (error) {
            client.logger.error('Erro na verificação manual:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao verificar o usuário.'
            );

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Remover verificação manual
     */
    async manualUnverify(interaction) {
        const embedStyles = new EmbedStyles();
        const client = interaction.client;
        const guild = interaction.guild;
        const user = interaction.options.getUser('usuario');

        await interaction.deferReply();

        try {
            const member = await guild.members.fetch(user.id).catch(() => null);
            if (!member) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Usuário Não Encontrado',
                    'Este usuário não está no servidor!'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Buscar configuração
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ?
            `).get(guild.id);

            if (!config) {
                const errorEmbed = embedStyles.createErrorEmbed(
                    'Sistema Não Configurado',
                    'Configure o sistema primeiro com `/verificacao setup`!'
                );
                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Remover verificação
            const verifiedRole = guild.roles.cache.get(config.verified_role_id);
            const unverifiedRole = config.unverified_role_id ? guild.roles.cache.get(config.unverified_role_id) : null;

            if (verifiedRole && member.roles.cache.has(verifiedRole.id)) {
                await member.roles.remove(verifiedRole);
            }

            if (unverifiedRole) {
                await member.roles.add(unverifiedRole);
            }

            // Atualizar banco
            client.database.db.prepare(`
                UPDATE member_verification
                SET verified = 0, verified_at = NULL, verification_method = NULL
                WHERE guild_id = ? AND user_id = ?
            `).run(guild.id, user.id);

            const successEmbed = embedStyles.createSuccessEmbed(
                'Verificação Removida',
                `A verificação de ${user.tag} foi removida com sucesso!\n\n• Cargo de verificado removido\n• Status atualizado no sistema\n• Log registrado`
            );

            await interaction.editReply({ embeds: [successEmbed] });

        } catch (error) {
            client.logger.error('Erro ao remover verificação:', error);

            const errorEmbed = embedStyles.createErrorEmbed(
                'Erro no Sistema',
                'Ocorreu um erro ao remover a verificação.'
            );

            await interaction.editReply({ embeds: [errorEmbed] });
        }
    },

    /**
     * Verificar membro (função auxiliar)
     */
    async verifyMember(member, config, method, verifiedBy = 'system') {
        const guild = member.guild;

        // Atribuir cargos
        const verifiedRole = guild.roles.cache.get(config.verified_role_id);
        const unverifiedRole = config.unverified_role_id ? guild.roles.cache.get(config.unverified_role_id) : null;

        if (verifiedRole) {
            await member.roles.add(verifiedRole);
        }

        if (unverifiedRole && member.roles.cache.has(unverifiedRole.id)) {
            await member.roles.remove(unverifiedRole);
        }

        // Atualizar banco
        member.client.database.db.prepare(`
            INSERT OR REPLACE INTO member_verification
            (guild_id, user_id, verified, verification_method, verified_at)
            VALUES (?, ?, 1, ?, CURRENT_TIMESTAMP)
        `).run(guild.id, member.id, method);

        // Log da verificação
        member.client.database.db.prepare(`
            INSERT INTO verification_logs
            (guild_id, user_id, action, method, details, timestamp)
            VALUES (?, ?, 'completed', ?, ?, CURRENT_TIMESTAMP)
        `).run(guild.id, member.id, method, JSON.stringify({ verifiedBy }));

        // Enviar mensagem de boas-vindas se configurada
        if (config.welcome_message) {
            try {
                const embedStyles = new EmbedStyles();
                const welcomeEmbed = embedStyles.createSuccessEmbed(
                    'Verificação Concluída!',
                    config.welcome_message
                );
                await member.send({ embeds: [welcomeEmbed] });
            } catch (error) {
                // Usuário não pode receber DM
            }
        }
    },

    /**
     * Obter display do método
     */
    getMethodDisplay(method) {
        const methods = {
            'reaction': '🎯 Reação em Embed',
            'captcha': '🧮 Captcha Matemático',
            'manual': '👥 Verificação Manual',
            'combined': '🔗 Combinado (Reação + Captcha)'
        };
        return methods[method] || method;
    },

    cooldown: 3,
    category: 'moderacao'
};
