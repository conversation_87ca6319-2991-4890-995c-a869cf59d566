/**
 * ========================================
 * COMANDO DE TESTE PARA SISTEMA DE LOGS
 * Comando para testar configuração de logs
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('test-logs')
        .setDescription('🧪 Testar sistema de logs e configurações')
        .addStringOption(option =>
            option.setName('tipo')
                .setDescription('Tipo de log para testar')
                .setRequired(false)
                .addChoices(
                    { name: '🗑️ Mensagem Deletada', value: 'message_delete' },
                    { name: '🤖 Auto-Moderação', value: 'auto_moderation' },
                    { name: '👥 Membro', value: 'member' },
                    { name: '🛡️ Moderação', value: 'moderation' },
                    { name: '📊 Configuração', value: 'config_check' }
                ))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false),

    async execute(interaction) {
        const client = interaction.client;
        const guild = interaction.guild;
        const tipo = interaction.options.getString('tipo') || 'config_check';

        await interaction.deferReply({ ephemeral: true });

        try {
            // Buscar configuração atual
            const guildConfig = client.guildConfigs?.get(guild.id);

            if (!guildConfig) {
                return await interaction.editReply({
                    content: '❌ Configuração do servidor não encontrada! Use `/config` para configurar o bot.'
                });
            }

            // Verificar configuração de logs
            const logsEnabled = guildConfig?.logs_enabled || guildConfig?.log_enabled;
            const generalLogChannel = guildConfig?.general_log_channel;
            const automodLogChannel = guildConfig?.automod_log_channel;
            const moderationLogChannel = guildConfig?.moderation_log_channel;
            const memberLogChannel = guildConfig?.member_log_channel;

            if (tipo === 'config_check') {
                // Mostrar configuração atual
                const configEmbed = new EmbedBuilder()
                    .setTitle('🔧 Configuração de Logs')
                    .setDescription('Estado atual do sistema de logs')
                    .setColor('#3498db')
                    .addFields(
                        { 
                            name: '📊 Status Geral', 
                            value: `**Logs Habilitados:** ${logsEnabled ? '✅ Sim' : '❌ Não'}\n**logs_enabled:** \`${guildConfig?.logs_enabled}\`\n**log_enabled:** \`${guildConfig?.log_enabled}\``, 
                            inline: false 
                        },
                        { 
                            name: '📺 Canais Configurados', 
                            value: `**🏠 Geral:** ${generalLogChannel ? `<#${generalLogChannel}>` : '❌ Não configurado'}\n**🤖 Auto-Moderação:** ${automodLogChannel ? `<#${automodLogChannel}>` : '❌ Não configurado'}\n**🛡️ Moderação:** ${moderationLogChannel ? `<#${moderationLogChannel}>` : '❌ Não configurado'}\n**👥 Membros:** ${memberLogChannel ? `<#${memberLogChannel}>` : '❌ Não configurado'}`, 
                            inline: false 
                        },
                        {
                            name: '🔍 Prioridade de Canais',
                            value: `Para **Auto-Moderação**:\n1. automod_log_channel\n2. general_log_channel\n3. log_channel_id\n4. log_channel`,
                            inline: false
                        }
                    )
                    .setFooter({ text: 'Nodex | Moderação - Teste de Logs' })
                    .setTimestamp();

                await interaction.editReply({ embeds: [configEmbed] });
                return;
            }

            // Verificar se logs estão habilitados
            if (!logsEnabled) {
                return await interaction.editReply({
                    content: '❌ Sistema de logs está desabilitado! Habilite no dashboard primeiro.'
                });
            }

            // Determinar canal baseado no tipo
            let targetChannelId;
            let channelType;

            switch (tipo) {
                case 'auto_moderation':
                case 'message_delete':
                    targetChannelId = automodLogChannel || generalLogChannel;
                    channelType = 'Auto-Moderação';
                    break;
                case 'moderation':
                    targetChannelId = moderationLogChannel || generalLogChannel;
                    channelType = 'Moderação';
                    break;
                case 'member':
                    targetChannelId = memberLogChannel || generalLogChannel;
                    channelType = 'Membros';
                    break;
                default:
                    targetChannelId = generalLogChannel;
                    channelType = 'Geral';
            }

            if (!targetChannelId) {
                return await interaction.editReply({
                    content: `❌ Canal de logs para **${channelType}** não configurado!`
                });
            }

            const targetChannel = guild.channels.cache.get(targetChannelId);
            if (!targetChannel) {
                return await interaction.editReply({
                    content: `❌ Canal de logs <#${targetChannelId}> não encontrado!`
                });
            }

            // Criar embed de teste
            const testEmbed = new EmbedBuilder()
                .setTitle(`🧪 Teste de Log - ${channelType}`)
                .setDescription(`Este é um teste do sistema de logs para verificar se está funcionando corretamente.`)
                .addFields(
                    { name: '🎯 Tipo de Teste', value: tipo.replace('_', ' ').toUpperCase(), inline: true },
                    { name: '📺 Canal', value: `<#${targetChannelId}>`, inline: true },
                    { name: '👤 Testado por', value: `${interaction.user.tag}`, inline: true },
                    { name: '🕐 Data/Hora', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
                )
                .setColor('#00ff7f')
                .setFooter({ text: 'Nodex | Moderação - Teste de Sistema' })
                .setTimestamp();

            // Enviar para o canal de logs
            await targetChannel.send({ embeds: [testEmbed] });

            // Confirmar sucesso
            await interaction.editReply({
                content: `✅ Teste de log enviado com sucesso!\n\n**Tipo:** ${channelType}\n**Canal:** <#${targetChannelId}>\n**Status:** Funcionando corretamente`
            });

            console.log(`🧪 [TEST LOGS] Teste enviado para ${channelType} em #${targetChannel.name} por ${interaction.user.tag}`);

        } catch (error) {
            console.error('❌ [TEST LOGS] Erro:', error);

            await interaction.editReply({
                content: `❌ Erro ao testar logs:\n\`\`\`${error.message}\`\`\``
            });
        }
    }
};
