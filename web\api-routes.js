const express = require('express');
const SecurityMiddleware = require('./middleware/security');
const ValidationMiddleware = require('./middleware/validation');
const PermissionsMiddleware = require('./middleware/permissions');

class APIRoutes {
    constructor(client, realTimeIntegration) {
        this.client = client;
        this.realTimeIntegration = realTimeIntegration;

        // CORREÇÃO CRÍTICA 7.3: Inicializar middleware de segurança
        this.securityMiddleware = new SecurityMiddleware();
        this.validationMiddleware = new ValidationMiddleware();
    }

    setupRoutes() {
        const router = express.Router();

        // CORREÇÃO CRÍTICA 7.3: API para salvar configurações com validação robusta
        router.post('/guild/:guildId/config/:section',
            // Middleware de segurança e validação
            this.securityMiddleware.apiRateLimit,
            this.validationMiddleware.validateGuildId,
            this.validationMiddleware.validateConfigSection,
            this.securityMiddleware.validateConfig,
            this.securityMiddleware.sanitizeInput,
            PermissionsMiddleware.requireGuildAccess,
            PermissionsMiddleware.requireConfigAccess(),
            this.securityMiddleware.auditLog('CONFIG_UPDATE'),
            async (req, res) => {
            try {
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    return res.status(401).json({ success: false, error: 'Não autenticado' });
                }

                const { guildId, section } = req.params;
                const configData = req.body;

            console.log(`🔄 [API] Salvando configurações de ${section} para guild ${guildId}:`, configData);

            try {
                // Obter configuração atual do banco
                let guildConfig = await this.client.database.getGuildConfig(guildId) || {};

                // Parse settings se necessário
                let settings = {};
                if (guildConfig.settings) {
                    try {
                        if (typeof guildConfig.settings === 'string') {
                            settings = JSON.parse(guildConfig.settings);
                        } else {
                            settings = guildConfig.settings;
                        }
                    } catch (parseError) {
                        console.error('❌ [API] Erro ao fazer parse das configurações:', parseError);
                        settings = {};
                    }
                }

                // Atualizar settings com os novos dados
                if (configData.settings) {
                    Object.assign(settings, configData.settings);
                }

                // Atualizar configuração principal se necessário
                if (configData.prefix) guildConfig.prefix = configData.prefix;
                if (configData.language) guildConfig.language = configData.language;
                if (configData.timezone) guildConfig.timezone = configData.timezone;
                if (configData.auto_mod_enabled !== undefined) guildConfig.auto_mod_enabled = configData.auto_mod_enabled;
                if (configData.anti_raid_enabled !== undefined) guildConfig.anti_raid_enabled = configData.anti_raid_enabled;

                // Salvar settings atualizadas
                guildConfig.settings = settings;

                // Salvar no banco de dados (com proteção contra loops)
                guildConfig._skipEmit = true; // Evitar loops infinitos
                await this.client.database.saveGuildConfig(guildId, guildConfig);
                delete guildConfig._skipEmit; // Limpar flag
                console.log(`✅ [API] Configurações salvas no banco para guild ${guildId}`);

                // Processamento específico por seção
                if (section === 'verificacao' && this.realTimeIntegration) {
                    console.log('🔄 [API] Processando configuração de verificação...');
                    this.client.emit('verificationUpdated', {
                        guildId: guildId,
                        config: configData.settings
                    });
                }

                // Aplicar configurações nos sistemas
                if (this.realTimeIntegration && this.realTimeIntegration.applyConfigToBot) {
                    await this.realTimeIntegration.applyConfigToBot(guildId, section, configData);
                }

                // Enviar notificação Discord
                if (this.client?.notificationManager) {
                    const guild = this.client.guilds.cache.get(guildId);
                    if (guild) {
                        await this.client.notificationManager.sendDashboardConfigUpdate(
                            guild,
                            section,
                            configData,
                            req.user.id
                        );
                    }
                }

                res.json({
                    success: true,
                    message: `Configurações de ${section} salvas com sucesso`,
                    section: section,
                    guildId: guildId,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('❌ [API] Erro ao salvar configurações:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor ao salvar configurações',
                    details: error.message
                });
            }
        });

        // CORREÇÃO CRÍTICA 7.3: API para carregar configurações com validação
        router.get('/guild/:guildId/config',
            this.securityMiddleware.apiRateLimit,
            this.validationMiddleware.validateGuildId,
            this.validationMiddleware.validateQueryParams,
            PermissionsMiddleware.requireGuildAccess,
            async (req, res) => {
            try {
                if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                    return res.status(401).json({ success: false, error: 'Não autenticado' });
                }

                const { guildId } = req.params;

            try {
                console.log(`📡 [API] Carregando configurações para guild ${guildId}`);

                // Buscar configuração do banco
                let guildConfig = await this.client.database.getGuildConfig(guildId);

                // Se não existir, criar configuração padrão
                if (!guildConfig) {
                    const defaultConfig = {
                        prefix: '!',
                        language: 'pt-BR',
                        timezone: 'America/Sao_Paulo',
                        auto_mod_enabled: true,
                        anti_raid_enabled: true,
                        settings: {
                            ai_moderation_enabled: false,
                            verification_enabled: false
                        }
                    };

                    await this.client.database.saveGuildConfig(guildId, defaultConfig);
                    guildConfig = defaultConfig;
                }

                // Parse settings se necessário
                let settings = {};
                if (guildConfig.settings) {
                    try {
                        if (typeof guildConfig.settings === 'string') {
                            settings = JSON.parse(guildConfig.settings);
                        } else {
                            settings = guildConfig.settings;
                        }
                    } catch (parseError) {
                        console.error('❌ [API] Erro ao fazer parse das configurações:', parseError);
                        settings = {};
                    }
                }

                // Retornar configuração completa
                const response = {
                    ...guildConfig,
                    settings: settings,
                    // Compatibilidade com o frontend
                    ai_moderation_enabled: settings.ai_moderation_enabled || false,
                    verification_enabled: settings.verification_enabled || false
                };

                console.log(`✅ [API] Configurações carregadas para guild ${guildId}:`, response);
                res.json(response);

            } catch (error) {
                console.error('❌ [API] Erro ao carregar configurações:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor ao carregar configurações',
                    details: error.message
                });
            }
        });

        // API para comandos
        router.get('/guild/:guildId/commands', (req, res) => {
            const defaultCommands = {
                ban: { enabled: true },
                kick: { enabled: true },
                warn: { enabled: true },
                warnings: { enabled: true },
                timeout: { enabled: true },
                mute: { enabled: true },
                unmute: { enabled: true },
                unban: { enabled: true },
                clear: { enabled: true },
                'mod-logs': { enabled: true },
                comandos: { enabled: true },
                backup: { enabled: true },
                'deploy-local': { enabled: true },
                'setup-panels': { enabled: true },
                stats: { enabled: true },
                dashboard: { enabled: true },
                config: { enabled: true },
                botinfo: { enabled: true },
                help: { enabled: true },
                recursos: { enabled: true },
                'stats-moderacao': { enabled: true },
                avatar: { enabled: true },
                ping: { enabled: true },
                say: { enabled: true },
                serverinfo: { enabled: true },
                userinfo: { enabled: true }
            };
            res.json({
                success: true,
                commands: defaultCommands,
                totalCommands: Object.keys(defaultCommands).length
            });
        });

        router.post('/guild/:guildId/commands', (req, res) => {
            const { commands } = req.body;
            setTimeout(() => {
                res.json({
                    success: true,
                    message: 'Estados dos comandos salvos com sucesso',
                    updatedCommands: commands ? Object.keys(commands).length : 0,
                    timestamp: new Date().toISOString()
                });
            }, 500);
        });

        // API de teste
        router.get('/test', (req, res) => {
            res.json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                server: 'Funcionando',
                client: this.client?.user?.username || 'Desconectado'
            });
        });

        // API para backup
        router.post('/guild/:guildId/backup/create', async (req, res) => {
            if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                return res.status(401).json({ success: false, error: 'Não autenticado' });
            }

            const { guildId } = req.params;
            const { options = {} } = req.body;

            try {
                console.log(`💾 [API] Criando backup para guild ${guildId}`);

                const result = await this.client.backup.createFullBackup(guildId, {
                    ...options,
                    createdBy: req.user.id
                });

                if (result.success) {
                    // Enviar notificação Discord
                    if (this.client?.notificationManager) {
                        const guild = this.client.guilds.cache.get(guildId);
                        if (guild) {
                            await this.client.notificationManager.sendBackupNotification(
                                guild,
                                'created',
                                { id: result.backup?.id || 'unknown', type: 'full' },
                                req.user.id
                            );
                        }
                    }

                    res.json({
                        success: true,
                        backup: result.backup,
                        message: 'Backup criado com sucesso!'
                    });
                } else {
                    res.status(500).json({
                        success: false,
                        error: result.message || 'Erro ao criar backup'
                    });
                }

            } catch (error) {
                console.error('❌ [API] Erro ao criar backup:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor ao criar backup'
                });
            }
        });

        // API para listar backups
        router.get('/guild/:guildId/backup/list', async (req, res) => {
            if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                return res.status(401).json({ success: false, error: 'Não autenticado' });
            }

            const { guildId } = req.params;

            try {
                console.log(`📋 [API] Listando backups para guild ${guildId}`);

                const backups = await this.client.backup.getGuildBackups(guildId);

                res.json({
                    success: true,
                    backups: backups || []
                });

            } catch (error) {
                console.error('❌ [API] Erro ao listar backups:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor ao listar backups'
                });
            }
        });

        // API para restaurar backup
        router.post('/guild/:guildId/backup/restore', async (req, res) => {
            if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                return res.status(401).json({ success: false, error: 'Não autenticado' });
            }

            const { guildId } = req.params;
            const { backupId, options = {} } = req.body;

            try {
                console.log(`🔄 [API] Restaurando backup ${backupId} para guild ${guildId}`);

                const result = await this.client.backup.restoreFromBackup(backupId, options);

                if (result.success) {
                    // Enviar notificação Discord
                    if (this.client?.notificationManager) {
                        const guild = this.client.guilds.cache.get(guildId);
                        if (guild) {
                            await this.client.notificationManager.sendBackupNotification(
                                guild,
                                'restored',
                                { backupId },
                                req.user.id
                            );
                        }
                    }

                    res.json({
                        success: true,
                        results: result.results,
                        message: result.message
                    });
                } else {
                    res.status(500).json({
                        success: false,
                        error: result.message || 'Erro ao restaurar backup'
                    });
                }

            } catch (error) {
                console.error('❌ [API] Erro ao restaurar backup:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor ao restaurar backup'
                });
            }
        });

        // API para deletar backup
        router.delete('/guild/:guildId/backup/delete', async (req, res) => {
            if (!req.isAuthenticated || !req.isAuthenticated() || !req.user) {
                return res.status(401).json({ success: false, error: 'Não autenticado' });
            }

            const { guildId } = req.params;
            const { backupId } = req.body;

            try {
                console.log(`🗑️ [API] Deletando backup ${backupId} para guild ${guildId}`);

                const result = await this.client.backup.deleteBackup(backupId);

                if (result.success) {
                    // Enviar notificação Discord
                    if (this.client?.notificationManager) {
                        const guild = this.client.guilds.cache.get(guildId);
                        if (guild) {
                            await this.client.notificationManager.sendBackupNotification(
                                guild,
                                'deleted',
                                { backupId },
                                req.user.id
                            );
                        }
                    }

                    res.json({
                        success: true,
                        message: 'Backup deletado com sucesso'
                    });
                } else {
                    res.status(500).json({
                        success: false,
                        error: 'Erro ao deletar backup'
                    });
                }

            } catch (error) {
                console.error('❌ [API] Erro ao deletar backup:', error);
                res.status(500).json({
                    success: false,
                    error: 'Erro interno do servidor ao deletar backup'
                });
            }
        });

        return router;
    }
}

module.exports = APIRoutes;