const { <PERSON>lash<PERSON><PERSON>mandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('debug-logs')
        .setDescription('🔍 Debug das configurações de logs (apenas administradores)')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        const { client, guild } = interaction;

        try {
            await interaction.deferReply({ ephemeral: true });

            // Buscar configurações do cache
            const cachedConfig = client.guildConfigs?.get(guild.id);
            
            // Buscar configurações do banco
            const dbConfig = await client.database.getGuildConfig(guild.id);

            // Parse das settings
            let settings = {};
            if (dbConfig?.settings) {
                try {
                    if (typeof dbConfig.settings === 'string') {
                        settings = JSON.parse(dbConfig.settings);
                    } else {
                        settings = dbConfig.settings;
                    }
                } catch (error) {
                    settings = { error: 'Erro ao fazer parse' };
                }
            }

            // Verificar canais
            const channels = {
                cache_log_channel_id: cachedConfig?.log_channel_id,
                cache_general_log_channel: cachedConfig?.general_log_channel,
                db_log_channel_id: dbConfig?.log_channel_id,
                settings_general_log_channel: settings.general_log_channel,
                settings_message_log_channel: settings.message_log_channel,
                settings_log_channel: settings.log_channel
            };

            // Encontrar canal ativo
            const activeChannelId = settings.general_log_channel ||
                                   settings.message_log_channel ||
                                   settings.log_channel ||
                                   dbConfig?.log_channel_id ||
                                   cachedConfig?.log_channel_id;

            const activeChannel = activeChannelId ? guild.channels.cache.get(activeChannelId) : null;

            // Verificar status dos logs
            const logStatus = {
                cache_logs_enabled: cachedConfig?.logs_enabled,
                cache_log_enabled: cachedConfig?.log_enabled,
                db_log_enabled: dbConfig?.log_enabled,
                settings_logs_enabled: settings.logs_enabled,
                settings_log_enabled: settings.log_enabled
            };

            // Determinar se logs estão ativos
            const logsActive = settings.logs_enabled || 
                              settings.log_enabled || 
                              cachedConfig?.logs_enabled || 
                              cachedConfig?.log_enabled ||
                              dbConfig?.log_enabled;

            const embed = new EmbedBuilder()
                .setColor(logsActive && activeChannel ? '#00ff00' : '#ff0000')
                .setTitle('🔍 Debug de Configurações de Logs')
                .setDescription(`**Status:** ${logsActive && activeChannel ? '✅ Funcionando' : '❌ Problema detectado'}`)
                .addFields(
                    {
                        name: '📊 Status dos Logs',
                        value: Object.entries(logStatus)
                            .map(([key, value]) => `**${key}:** ${value === true ? '✅' : value === false ? '❌' : '❓ Não definido'}`)
                            .join('\n') || 'Nenhuma configuração encontrada',
                        inline: false
                    },
                    {
                        name: '📺 Canais Configurados',
                        value: Object.entries(channels)
                            .filter(([key, value]) => value)
                            .map(([key, value]) => {
                                const channel = guild.channels.cache.get(value);
                                return `**${key}:** ${channel ? `<#${value}>` : `❌ Canal não encontrado (${value})`}`;
                            })
                            .join('\n') || 'Nenhum canal configurado',
                        inline: false
                    },
                    {
                        name: '🎯 Canal Ativo',
                        value: activeChannel 
                            ? `✅ <#${activeChannel.id}> (${activeChannel.name})`
                            : '❌ Nenhum canal ativo encontrado',
                        inline: false
                    },
                    {
                        name: '🔧 Cache vs Banco',
                        value: `**Cache existe:** ${cachedConfig ? '✅' : '❌'}\n**Banco existe:** ${dbConfig ? '✅' : '❌'}\n**Settings válidas:** ${Object.keys(settings).length > 0 ? '✅' : '❌'}`,
                        inline: false
                    },
                    {
                        name: '💡 Diagnóstico',
                        value: getDiagnostic(logsActive, activeChannel, settings, dbConfig, cachedConfig),
                        inline: false
                    }
                )
                .setTimestamp()
                .setFooter({ text: 'Debug de Logs • Nodex | Moderação' });

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('❌ [DEBUG LOGS] Erro:', error);
            await interaction.editReply({
                content: `❌ **Erro no debug:** ${error.message}`,
                ephemeral: true
            });
        }
    }
};

function getDiagnostic(logsActive, activeChannel, settings, dbConfig, cachedConfig) {
    const issues = [];
    const solutions = [];

    if (!logsActive) {
        issues.push('❌ Logs desabilitados');
        solutions.push('• Ative os logs no dashboard: "Logs & Auditoria" > "Ativar Logs"');
    }

    if (!activeChannel) {
        issues.push('❌ Canal de logs não configurado');
        solutions.push('• Configure o canal no dashboard: "Logs & Auditoria" > "Canal de Logs Gerais"');
        solutions.push('• Ou use o comando: `/config canal-logs #seu-canal`');
    }

    if (!cachedConfig) {
        issues.push('⚠️ Cache não encontrado');
        solutions.push('• Reinicie o bot ou aguarde alguns minutos');
    }

    if (!dbConfig) {
        issues.push('❌ Configuração não existe no banco');
        solutions.push('• Configure o servidor no dashboard');
    }

    if (Object.keys(settings).length === 0) {
        issues.push('⚠️ Settings vazias');
        solutions.push('• Configure módulos no dashboard');
    }

    if (issues.length === 0) {
        return '✅ **Tudo funcionando corretamente!**\n\nOs logs devem estar sendo enviados normalmente.';
    }

    return `**Problemas encontrados:**\n${issues.join('\n')}\n\n**Soluções:**\n${solutions.join('\n')}`;
}
