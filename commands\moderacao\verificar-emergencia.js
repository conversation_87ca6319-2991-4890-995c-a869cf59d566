/**
 * ========================================
 * COMANDO DE EMERGÊNCIA PARA VERIFICAÇÃO
 * Comando para recriar mensagem de verificação
 * ========================================
 */

const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('verificar-emergencia')
        .setDescription('🚨 Comando de emergência para recriar mensagem de verificação')
        .addUserOption(option =>
            option.setName('usuario')
                .setDescription('Verificar usuário específico manualmente')
                .setRequired(false))
        .addBooleanOption(option =>
            option.setName('recriar_mensagem')
                .setDescription('Recriar mensagem de verificação no canal')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .setDMPermission(false),

    async execute(interaction) {
        const client = interaction.client;
        const guild = interaction.guild;
        const user = interaction.options.getUser('usuario');
        const recriarMensagem = interaction.options.getBoolean('recriar_mensagem');

        await interaction.deferReply({ ephemeral: true });

        try {
            // Buscar configuração de verificação
            const config = client.database.db.prepare(`
                SELECT * FROM verification_config WHERE guild_id = ? AND enabled = 1
            `).get(guild.id);

            if (!config) {
                return await interaction.editReply({
                    content: '❌ Sistema de verificação não configurado neste servidor!'
                });
            }

            console.log(`🚨 [EMERGENCY] Configuração encontrada:`, {
                method: config.method,
                verification_method: config.verification_method,
                channel_id: config.channel_id,
                verified_role_id: config.verified_role_id,
                message_id: config.message_id
            });

            if (user) {
                // Verificar usuário específico
                const member = guild.members.cache.get(user.id);
                if (!member) {
                    return await interaction.editReply({
                        content: '❌ Usuário não encontrado no servidor!'
                    });
                }

                // Completar verificação manualmente
                await client.verificationSystem.completeVerification(
                    member,
                    config,
                    'emergency_manual',
                    'admin_override'
                );

                await interaction.editReply({
                    content: `✅ Usuário ${user.tag} foi verificado manualmente com sucesso!`
                });

            } else if (recriarMensagem) {
                // Recriar mensagem de verificação
                console.log(`🚨 [EMERGENCY] Recriando mensagem de verificação...`);

                if (!client.verificationSystem) {
                    return await interaction.editReply({
                        content: '❌ Sistema de verificação não disponível!'
                    });
                }

                // Usar o método do sistema de verificação
                await client.verificationSystem.createVerificationMessage(guild, {
                    verification_channel_id: config.channel_id,
                    verified_role_id: config.verified_role_id,
                    unverified_role_id: config.unverified_role_id,
                    verification_method: config.verification_method || config.method,
                    verification_captcha_type: config.verification_captcha_type || config.captcha_type,
                    verification_timeout_minutes: config.timeout_minutes,
                    verification_rules_text: config.rules_text,
                    verification_dm_enabled: config.dm_enabled,
                    verification_log_enabled: config.log_enabled,
                    verification_auto_kick: config.auto_kick,
                    verification_kick_time: config.kick_time,
                    verification_max_attempts: config.max_attempts,
                    verification_anti_bot: config.anti_bot_enabled,
                    verification_ip_check: config.ip_check_enabled,
                    welcome_message: config.welcome_message,
                    combined_captcha: config.combined_captcha,
                    combined_rules: config.combined_rules
                });

                await interaction.editReply({
                    content: `✅ Mensagem de verificação recriada com sucesso!\n\n**Método:** ${config.verification_method || config.method}\n**Canal:** <#${config.channel_id}>`
                });

            } else {
                // Mostrar informações de debug
                const channel = guild.channels.cache.get(config.channel_id);
                const verifiedRole = guild.roles.cache.get(config.verified_role_id);

                const debugInfo = `🚨 **INFORMAÇÕES DE DEBUG**

**📊 Configuração Atual:**
• **Método:** \`${config.verification_method || config.method}\`
• **Canal:** ${channel ? `<#${config.channel_id}>` : '❌ Canal não encontrado'}
• **Cargo:** ${verifiedRole ? `@${verifiedRole.name}` : '❌ Cargo não encontrado'}
• **Message ID:** \`${config.message_id || 'N/A'}\`

**🔧 Campos do Banco:**
• **method:** \`${config.method}\`
• **verification_method:** \`${config.verification_method}\`
• **captcha_type:** \`${config.captcha_type}\`
• **verification_captcha_type:** \`${config.verification_captcha_type}\`

**⚡ Ações Disponíveis:**
• Use \`/verificar-emergencia recriar_mensagem:True\` para recriar a mensagem
• Use \`/verificar-emergencia usuario:@user\` para verificar alguém manualmente
• Use \`/verificacao status\` para ver o status completo`;

                await interaction.editReply({
                    content: debugInfo
                });
            }

        } catch (error) {
            console.error('❌ [EMERGENCY] Erro:', error);
            client.logger.error('Erro no comando de emergência:', error);

            await interaction.editReply({
                content: `❌ Erro no comando de emergência:\n\`\`\`${error.message}\`\`\``
            });
        }
    }
};
